{"name": "@toolsdk.ai/web", "version": "2.0.0-beta.32", "private": true, "scripts": {"dev": "USE_TURBOPACK=true  next dev --turbopack --port 3002", "build": "NODE_OPTIONS=--max-old-space-size=8192 next build && npm run _build-prisma", "_build-prisma": "mkdir -p ./.next/standalone/node_modules/.prisma/client && cp -r ../../packages/@toolsdk.ai/orm/prisma/prisma-client/* ./.next/standalone/node_modules/.prisma/client", "test": "vitest", "start": "next start", "lint": "biome format . --write && biome check ."}, "dependencies": {"@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "@clerk/nextjs": "^6.7.6", "@mdx-js/loader": "^3.0.0", "@mdx-js/react": "^3.0.0", "@next/mdx": "15.3.3", "@toolsdk.ai/domain": "workspace:*", "@toolsdk.ai/mcp-server": "workspace:*", "@toolsdk.ai/orm": "workspace:*", "@toolsdk.ai/sdk-ts": "workspace:*", "@types/mdx": "^2.0.10", "axios": "^1.10.0", "basenext": "workspace:*", "live-plugin-manager": "^1.0.0", "next": "15.3.3", "random-words": "^2.0.1", "react": "18.3.1", "react-dom": "18.3.1", "sharelib": "workspace:*", "zod": "^3.25.0"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18", "@types/react-dom": "^18", "msw": "^2.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.2", "typescript": "^5.8.3", "vitest": "^3.2.4"}}