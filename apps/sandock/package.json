{"name": "@sandock/web", "private": true, "version": "2.0.0-beta.32", "type": "module", "scripts": {"dev": "next dev --port 3030", "build": "pnpm run docs:build && next build", "start": "next start -p 3030", "shell:dev": "ts-node shell-server.ts", "docs:dev": "pnpm run docs:serve", "docs:build": "docusaurus build docs-site --out-dir ../public/docs-build", "docs:serve": "docusaurus serve docs-site --dir ../public/docs-build", "docs:clear": "docusaurus clear docs-site", "docs:swizzle": "docusaurus swizzle docs-site", "docs:deploy": "docusaurus deploy docs-site", "docs:write-translations": "docusaurus write-translations docs-site", "docs:write-heading-ids": "docusaurus write-heading-ids docs-site", "docs:typecheck": "tsc -p docs-site/tsconfig.json", "lint": "biome format . --write && biome check ."}, "dependencies": {"@clerk/nextjs": "^6.7.6", "@docusaurus/core": "3.8.1", "@docusaurus/preset-classic": "3.8.1", "@hono/zod-openapi": "^0.16.4", "@mdx-js/react": "^3.0.0", "@mui/joy": "^5.0.0-beta.52", "@sandock/domains": "workspace:*", "@tanstack/react-query": "^5.85.1", "@toolsdk.ai/domain": "workspace:*", "@trpc/client": "^11.4.4", "@trpc/react-query": "^11.4.4", "@trpc/server": "^11.4.4", "basenext": "workspace:*", "clsx": "^2.0.0", "hono": "^4.8.4", "lucide-react": "0.542.0", "next": "15.3.3", "prism-react-renderer": "^2.3.0", "react": "18.3.1", "react-dom": "18.3.1", "sharelib": "workspace:*", "ws": "^8.18.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.8.1", "@docusaurus/tsconfig": "3.8.1", "@docusaurus/types": "3.8.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}