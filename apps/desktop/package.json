{"name": "@bika/desktop", "private": true, "productName": "desktop", "version": "2.0.0-beta.32", "scripts": {"run": "tauri dev", "dev": "next dev --turbopack -p 3030", "build": "NODE_OPTIONS=--max-old-space-size=8192 next build", "start": "next start", "lint": "biome format . --write && biome check .", "build-staging:tauri:arm64": "MODE=staging tauri build --target aarch64-apple-darwin --debug", "build-staging:tauri:x86": "MODE=staging tauri build --target x86_64-apple-darwin --debug", "build-product:tauri": "MODE=product tauri build", "tauri": "tauri"}, "dependencies": {"@bika/api-caller": "workspace:*", "@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "@tauri-apps/plugin-deep-link": "^2.2.0", "@tauri-apps/plugin-opener": "^2.2.6", "@tauri-apps/plugin-store": "^2.2.0", "next": "15.3.3", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@tauri-apps/cli": "^2.4.0", "@types/node": "^20.11.24", "@types/react": "^18", "@types/react-dom": "^18", "tailwindcss": "^3.4.11", "typescript": "^5.8.3"}}