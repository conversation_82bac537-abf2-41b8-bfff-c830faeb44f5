'use client';

import { useLocale } from '@bika/contents/i18n';
import { useCssColor } from '@bika/ui/colors';
import { Button } from '../button-component';
import { Stack } from '../layout-components';
import { Typography } from '../text-components';
import { CONST_INVITE_CODE_INVITE_COIN } from './invite-code';

export function MembershipUpgradeComponent(props: {
  handleReferralCode?: () => void;
  handleNavigateMPremium?: () => void;
}) {
  const { handleReferralCode, handleNavigateMPremium } = props;
  const { t } = useLocale();
  const colors = useCssColor();

  return (
    <div>
      <Stack
        direction="column"
        // className="max-w-[520px] px-[48px]"
        sx={{
          width: '100%',
          // maxWidht: '520px',
          // paddingX: '48px',
        }}
      >
        {/* <IconButton>
          <CloseOutlined></CloseOutlined>
        </IconButton> */}

        <Stack alignItems="center" justifyContent="center">
          <Typography level="h4" textColor={'var(--text-primary)'}>
            {t.wizard.membership_modal_title}
          </Typography>

          <Typography
            level={'b2'}
            textColor={'var(--text-secondary)'}
            sx={{
              marginTop: '32px',
              textAlign: 'left',
            }}
          >
            {t.wizard.bika_tips1}
          </Typography>
        </Stack>

        <Stack
          direction="column"
          gap="8px"
          sx={{
            marginTop: '32px',
          }}
        >
          <Typography level="b2" textColor={'var(--text-primary)'}>
            {t.wizard.bika_tip1_title}
          </Typography>

          <Typography level="b2" textColor={'var(--text-secondary)'}>
            {t('wizard.bika_tip1_description', {
              count: <span style={{ color: colors.rainbowYellow5 }}>3,000</span>,
            })}
          </Typography>
        </Stack>

        <Stack
          direction="column"
          gap="8px"
          sx={{
            marginTop: '32px',
          }}
        >
          <Typography level="b2" textColor="var(--text-primary)">
            {t.wizard.bika_upgraded_tip2_title}
          </Typography>

          <Typography textColor="var(--text-secondary)">
            {t('wizard.bika_upgraded_tip2_description', {
              count: (
                <span style={{ color: colors.rainbowYellow5 }}>
                  {t('wizard.credits', {
                    sum: CONST_INVITE_CODE_INVITE_COIN,
                  })}
                </span>
              ),
            })}
          </Typography>
        </Stack>
        {/* describe footer  */}
        <Stack
          direction="row"
          gap="16px"
          justifyContent="center"
          sx={{
            marginTop: '48px',
            // paddingTop: '24px',
          }}
        >
          <Button
            variant="fill"
            color="default"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleReferralCode?.();
            }}
            sx={{
              flex: 1,
              height: '48px',
            }}
          >
            {t.wizard.check_invite_code}
          </Button>
          <Button
            variant="solid"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleNavigateMPremium?.();
            }}
            sx={{
              flex: 1,
              height: '48px',
            }}
          >
            {t.wizard.navigate_premium_plan}
          </Button>
        </Stack>
      </Stack>
    </div>
  );
}
