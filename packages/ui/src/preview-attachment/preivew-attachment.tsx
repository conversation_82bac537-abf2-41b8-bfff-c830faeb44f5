import type { Translate } from '@bika/contents/i18n/translate';
import { MUIModal } from '@bika/ui/modal';
import { SnackbarProvider } from '@bika/ui/snackbar';
import { cn } from '@bika/ui/utils';
import Image from 'next/image';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { useKey } from 'react-use';
import { isImage, isSupportImage } from './attachment-renderer';
import { initTransformInfo, initTranslatePosition, MAX_SCALE, MIN_SCALE } from './const';
import { PreviewImageProvider } from './context/preview-image-context';
import { FilePreview } from './file-preview';
import { useFrameSetState } from './hooks/use-frame-state';
import type { ISimpleAttachment, ITransFormInfo } from './interface';
import { PreviewImage } from './preview-image';
import { ToolBar } from './tool_bar';
import { getAttachmentDisplayUrl } from './utils/get-attachment-display-path';

interface IAttachmentPreviewProps {
  index: number;
  attachments: ISimpleAttachment[];
  onClose: () => void;
  t: Translate;
}
// 预览导航 Hook
function usePreviewNavigation(attachments: ISimpleAttachment[], initialIndex: number) {
  const [activeIndex, setActiveIndex] = React.useState(initialIndex);
  const showPrevBtn = activeIndex !== 0;
  const showNextBtn = activeIndex !== attachments.length - 1;
  const currentAttachment = attachments[activeIndex];

  const next = React.useCallback(() => {
    setActiveIndex((prev) => Math.min(prev + 1, attachments.length - 1));
  }, [attachments.length]);

  const prev = React.useCallback(() => {
    setActiveIndex((prev) => Math.max(0, prev - 1));
  }, []);

  return {
    activeIndex,
    setActiveIndex,
    currentAttachment,
    showPrevBtn,
    showNextBtn,
    next,
    prev,
  };
}

// 变换相关 Hook
export function useTransform() {
  const [transformInfo, setTransformInfo] = useFrameSetState<ITransFormInfo>(initTransformInfo);

  const handleZoom = React.useCallback(
    (newScale: number) => {
      const { initActualScale } = transformInfo;
      const minTransformScale = MIN_SCALE / initActualScale;
      const maxTransformScale = MAX_SCALE / initActualScale;

      setTransformInfo((state) => ({
        ...state,
        scale: Math.min(Math.max(newScale, minTransformScale), maxTransformScale),
        translatePosition: initTranslatePosition,
      }));
    },
    [transformInfo, setTransformInfo],
  );

  const handleRotate = React.useCallback(() => {
    setTransformInfo((state) => ({
      ...state,
      rotate: (state.rotate || 0) + 90,
      translatePosition: initTranslatePosition,
    }));
  }, [setTransformInfo]);

  return {
    transformInfo,
    setTransformInfo,
    handleZoom,
    handleRotate,
  };
}

// 缩略图组件
const ThumbnailList: React.FC<{
  attachments: ISimpleAttachment[];
  activeIndex: number;
  onSelect: (index: number) => void;
}> = ({ attachments, activeIndex, onSelect }) => (
  <div className={'h-[80px] space-x-4 flex items-center justify-center'}>
    {attachments.map((attachment, index) => (
      <div key={attachment.url} onClick={(e) => e.stopPropagation()}>
        <img
          src={getAttachmentDisplayUrl(attachment)}
          alt=""
          loading="lazy"
          className={cn(
            'object-contain w-[40px] h-[40px] opacity-50 cursor-pointer',
            activeIndex === index && 'opacity-100 border-[2px] border-[white]',
          )}
          onClick={() => onSelect(index)}
        />
      </div>
    ))}
  </div>
);

// 导航按钮组件
const NavigationButton: React.FC<{
  direction: 'prev' | 'next';
  show: boolean;
  onClick: () => void;
}> = ({ direction, show, onClick }) => {
  if (!show) return null;

  return (
    <Image
      src={`/assets/attachment/${direction === 'prev' ? 'previous' : 'next'}_filled.svg`}
      alt=""
      width={40}
      height={40}
      className={'cursor-pointer'}
      onClick={onClick}
    />
  );
};

// 主预览组件
const PreviewAttachment: React.FC<IAttachmentPreviewProps> = ({
  attachments,
  onClose,
  index: initIndex,
  t,
}) => {
  const { activeIndex, setActiveIndex, currentAttachment, showPrevBtn, showNextBtn, next, prev } =
    usePreviewNavigation(
      // attachments:
      attachments as ISimpleAttachment[],
      initIndex,
    );

  const { transformInfo, setTransformInfo, handleZoom, handleRotate } = useTransform();
  const type = currentAttachment?.contentType;

  const isImageEditEnabled =
    isImage({
      name: currentAttachment.name,
      type,
    }) && isSupportImage(type ?? '');

  useKey('Escape', onClose, { event: 'keyup' });
  useKey('ArrowRight', next);
  useKey('ArrowLeft', prev);

  return (
    <SnackbarProvider>
      <MUIModal open>
        <div className={'w-full h-full'} onClick={onClose}>
          <div
            className={
              'w-full h-[64px] bg-[rgba(0,0,0,.9)] flex items-center justify-between px-10'
            }
            onClick={(e) => e.stopPropagation()}
          >
            <ToolBar
              transformInfo={transformInfo}
              setTransformInfo={setTransformInfo}
              fileInfo={currentAttachment}
              onClose={onClose}
              onZoom={handleZoom}
              onRotate={handleRotate}
              t={t}
            />
          </div>

          <div className={'flex h-[calc(100%-64px-80px)] py-4'}>
            <div
              className={'w-[136px] flex justify-center items-center'}
              onClick={(e) => {
                if (attachments.length === 1) {
                  onClose?.();
                }
                e.stopPropagation();
              }}
            >
              <NavigationButton direction="prev" show={showPrevBtn} onClick={prev} />
            </div>

            <div className={'w-[full] h-full flex-1 flex items-center overflow-hidden'}>
              {!isImageEditEnabled ? (
                <FilePreview
                  onClose={onClose}
                  url={currentAttachment.url}
                  fileName={currentAttachment.name}
                />
              ) : (
                <PreviewImageProvider
                  transformInfo={transformInfo}
                  setTransformInfo={setTransformInfo}
                >
                  <PreviewImage file={currentAttachment} />
                </PreviewImageProvider>
              )}
            </div>

            <div
              className={'w-[136px] flex justify-center items-center'}
              onClick={(e) => {
                if (attachments.length === 1) {
                  onClose?.();
                }
                e.stopPropagation();
              }}
            >
              <NavigationButton direction="next" show={showNextBtn} onClick={next} />
            </div>
          </div>

          <ThumbnailList
            attachments={attachments as ISimpleAttachment[]}
            activeIndex={activeIndex}
            onSelect={setActiveIndex}
          />
        </div>
      </MUIModal>
    </SnackbarProvider>
  );
};

// 触发预览的工具函数
const triggerPreviewAttachment = (params: Omit<IAttachmentPreviewProps, 'onClose'>) => {
  const div = document.createElement('div');
  document.body.appendChild(div);
  const root = createRoot(div);

  const onClose = () => {
    root.unmount();
    document.body.removeChild(div);
  };

  root.render(<PreviewAttachment {...params} onClose={onClose} />);
};

export { PreviewAttachment, triggerPreviewAttachment };
