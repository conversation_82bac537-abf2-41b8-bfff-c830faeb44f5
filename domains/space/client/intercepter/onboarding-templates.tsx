'use client';

import { useTRPCQuery } from '@bika/api-caller/context';
import { useInstaller } from '@bika/domains/ai-skillset/bika-app-builder/use-installer';
import { useSpaceContextForce } from '@bika/types/space/context';
import type { OnboardingPage } from '@bika/types/space/vo';
import { Modal } from '@bika/ui/modal';
import { useEffect, useState } from 'react';
import { OnboardingTemplatesComponent } from './onboarding-templates-component';
import { useBuildInstallGlobalStore } from './use-build-install-global-store';

export function OnboardingTemplates() {
  const trpcQuery = useTRPCQuery();
  const spaceContext = useSpaceContextForce();

  const updateSpace = trpcQuery.space.update.useMutation();

  const [installing, setInstalling] = useState(false);
  const installTemplates = useInstaller();

  const { setData } = useBuildInstallGlobalStore();
  const trpcUtils = trpcQuery.useUtils();

  useEffect(() => {
    setData(undefined);
  }, [setData]);

  const handleUpdateOnboarding = async () => {
    const newOnboardings: OnboardingPage[] = [
      ...(spaceContext.data.settings.onboardings || []),
      'ONBOARDING_TEMPLATES',
    ];

    await updateSpace.mutateAsync({
      id: spaceContext.data.id,
      data: {
        settings: {
          onboardings: newOnboardings,
        },
      },
    });

    // 刷新 space settings
    trpcUtils.space.info.invalidate();
  };

  const handleTemplateInstall = async (data: string[]) => {
    if (data?.length) {
      await installTemplates(data, async () => {
        // Complete onboarding after template installation
        await handleUpdateOnboarding();
        setInstalling(true);
      });
    } else {
      await handleUpdateOnboarding();
      setInstalling(true);
    }
  };

  if (installing) {
    return null;
  }
  return (
    <Modal
      closable={false}
      onClose={handleUpdateOnboarding}
      sx={{
        backgroundImage: `url(/assets/wizards/limit_modal_bg.png)`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        width: 'min-content',
        minWidth: '818px',
        overflowY: 'hidden',
        paddingTop: '32px',
      }}
    >
      <OnboardingTemplatesComponent
        handleChange={handleTemplateInstall}
      ></OnboardingTemplatesComponent>
    </Modal>
  );
}
