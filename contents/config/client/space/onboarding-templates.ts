import type { iString } from 'basenext/i18n';
import { useLocale } from '../../../i18n/context';

export const OnboardingRoles = [
  // One-person company,
  'ceo',

  // Marketer
  'marketer',
  'sales',
  'support',

  // Product
  'product',

  'finance',
] as const;
export type OnboardingRole = (typeof OnboardingRoles)[number];

type IOnboardingTemplatesConfig = {
  name: iString;
  description: iString;
  templateIds: string[];
};

export const useOnboardingTemplateMap = () => {
  const { t } = useLocale();
  const OnboardingTemplateMap: Record<OnboardingRole, IOnboardingTemplatesConfig> = {
    ceo: {
      name: t.wizard.onboarding_role.ceo_name,
      description: t.wizard.onboarding_role.ceo_description,
      templateIds: [
        'agent-brand-designer',
        'agent-email-marketer',
        'agent-twitter-manager',
        'agent-google-analyst',
        'simple-powerful-crm',
        'agent-ai-writer',
      ],
    },
    marketer: {
      name: t.wizard.onboarding_role.marketer_name,
      description: t.wizard.onboarding_role.marketer_description,
      templateIds: [
        'agent-brand-designer',
        'agent-twitter-manager',
        'agent-email-marketer',
        'agent-google-analyst',
        'agent-ai-writer',
      ],
    },
    sales: {
      name: t.wizard.onboarding_role.sales_name,
      description: t.wizard.onboarding_role.sales_description,
      templateIds: ['simple-powerful-crm', 'agent-email-marketer'],
    },
    product: {
      name: t.wizard.onboarding_role.product_name,
      description: t.wizard.onboarding_role.product_description,
      templateIds: [
        'agent-ai-programmer',
        'agent-github-issue-creator',
        'agent-requirements-document-writer',
        'agent-discourse-community-manager',
        'lightweight-product-rnd-management',
      ],
    },
    support: {
      name: t.wizard.onboarding_role.support_name,
      description: t.wizard.onboarding_role.support_description,
      templateIds: [
        'agent-customer-support-scribe',
        'agent-discourse-community-manager',
        'agent-ticket-manager',
        'agent-community-reporter',
      ],
    },
    finance: {
      name: t.wizard.onboarding_role.finance_name,
      description: t.wizard.onboarding_role.finance_description,
      templateIds: ['agent-stock-news-reporter', 'agent-office-docs-helper', 'hr-knowledge-base'],
    },
  };

  return OnboardingTemplateMap;
};
