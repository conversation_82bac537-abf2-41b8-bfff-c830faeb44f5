const dict = {
  about: {
    about: 'について',
    address: '488 The Bridle Walk, Toronto, ON L6C 2Y4 Canada',
    cn_license: '备案号：粤ICP备********号-4 公安备案号：**************',
    copyright: '著作権 © 2025 Bika.ai',
    license: 'ライセンス',
    permission: 'アプリ権限説明',
    privacy_policy: 'プライバシーポリシー',
    rate_us: '評価してください',
    release_notes: '変更履歴',
    safety: '安全ホワイトペーパー',
  },
  account: {
    account_binding: 'アカウント連携',
    account_binding_and_security: 'アカウント連携とセキュリティ',
    account_binding_description:
      'アラートを受け取り、タスクを処理するために、次の方法のいずれかでBikaアカウントにログインしてください。',
    account_binding_error: 'アカウントバインディングエラー',
    account_binding_error_description:
      'アカウントバインディングエラーが発生しました。もう一度お試しください。',
    account_binding_error_type: 'この{type}はすでに他のアカウントにバインドされています',
    account_binding_success: 'アカウントバインディング成功',
    account_info: 'アカウント情報',
    advanced_features: '高度な機能',
    bind_now: '今すぐ連携',
    bound: 'バウンド',
    delete_account: 'アカウントを削除',
    delete_account_description:
      'アカウントを削除すると、アカウント内のすべてのデータが削除されます。本当にアカウントを削除しますか？',
    destroy_account: 'アカウントを削除',
    social_account_binding: 'ソーシャルアカウント連携',
    social_account_binding_description:
      'ソーシャルアカウントを連携して、さらに多くの機能をアンロックします',
    subscribe_now: '今すぐ購読',
    use_advanced_features: '高度な機能を使用',
    use_advanced_features_description: '高度な機能を開いてチームの効率を高めます',
  },
  action: {
    accept: '受け入れ',
    accepted: '受け入れました',
    add: '追加',
    added: '追加しました',
    adding: '追加中...',
    again: '再度',
    apply: '適用',
    auth: '認証',
    back: '戻る',
    cancel: 'キャンセル',
    choose: '選択してください',
    click_here: 'ここをクリック',
    close: '閉じる',
    coming_soon: '近日公開',
    coming_soon_description: 'この機能は近日公開されます',
    comment: 'コメント',
    commented: 'コメントしました',
    complete: '完了',
    completed: '完了',
    confirm: '確認',
    connect: '接続する',
    connected: '接続済み',
    contact_community: 'コミュニティに連絡',
    create: '作成',
    create_data: '新しいデータ',
    create_folder: 'フォルダを作成',
    create_resource: 'リソースを作成',
    created: '作成しました',
    current: '現在',
    decline: '辞退',
    declined: '辞退しました',
    delete: '削除',
    delete_resource: 'リソースを削除',
    deleted: '削除しました',
    detail: '詳細',
    due: '期限',
    duplicate: '複製',
    edit: '編集',
    edit_resource: 'リソースを編集',
    edited: '編集しました',
    email_sent: 'メールが送信されました',
    failed: '失敗',
    get: '取得',
    import: 'インポート',
    install: 'インストール',
    installed: 'インストールしました',
    loading: '読み込み中...',
    manual_complete: '手動完了',
    more: 'もっと',
    move: '移動',
    next: '次へ',
    no: 'いいえ',
    not_supported: 'サポートされていません',
    not_supported_description: 'この機能は現在サポートされていません',
    ok: 'OK',
    only_on_web: 'Web版のみ',
    only_on_web_description: 'この機能はWeb版のみで利用できます',
    only_on_web_editor: 'エディター',
    only_on_web_editor_description: 'この機能はWeb版のみで利用できます。エディターを開いてください',
    overdue: '期限切れ',
    process: '処理',
    processing: '処理中',
    reauthorize_integration: '再認証',
    redirect: 'リダイレクト',
    remove: '削除',
    removed: '削除しました',
    rename: '名前を変更',
    save: '保存',
    saved: '保存しました',
    search: '検索',
    search_placeholder: '検索...',
    search_result: '検索結果',
    search_result_description: '「{keyword}」の検索結果',
    search_result_not_found: '検索結果が見つかりません',
    search_result_not_found_description: '「{keyword}」に一致する結果が見つかりません',
    searching: '検索中',
    select: '選択',
    select_all: 'すべて選択',
    selected: '選択しました',
    send: '送信',
    submit: '送信',
    submitted: '送信しました',
    submitter: '提出者',
    transfer: '転送',
    transferred: '転送しました',
    uninstall: 'アンインストール',
    uninstalled: 'アンインストールしました',
    unselect_all: 'すべて選択解除',
    view: '表示',
    viewed: '表示しました',
    yes: 'はい',
  },
  symbol: {
    comma: '、',
  },
  ag_grid: {
    AreaColumnCombo: 'エリアとカラム',
    addCurrentSelectionToFilter: '現在の選択をフィルターに追加',
    addToLabels: '${variable}をラベルに追加',
    addToValues: '${variable}を値に追加',
    advancedFilterAnd: 'かつ',
    advancedFilterApply: '適用',
    advancedFilterBlank: '空白',
    advancedFilterBuilder: 'ビルダー',
    advancedFilterBuilderAddButtonTooltip: 'フィルターまたはグループを追加',
    advancedFilterBuilderAddCondition: 'フィルターを追加',
    advancedFilterBuilderAddJoin: 'グループを追加',
    advancedFilterBuilderApply: '適用',
    advancedFilterBuilderCancel: 'キャンセル',
    advancedFilterBuilderEnterValue: '値を入力...',
    advancedFilterBuilderMoveDownButtonTooltip: '下へ移動',
    advancedFilterBuilderMoveUpButtonTooltip: '上へ移動',
    advancedFilterBuilderRemoveButtonTooltip: '削除',
    advancedFilterBuilderSelectColumn: '列を選択',
    advancedFilterBuilderSelectOption: 'オプションを選択',
    advancedFilterBuilderTitle: '高度なフィルター',
    advancedFilterBuilderValidationAlreadyApplied: '現在のフィルターは既に適用されています。',
    advancedFilterBuilderValidationEnterValue: '値を入力する必要があります。',
    advancedFilterBuilderValidationIncomplete: 'すべての条件が完了していません。',
    advancedFilterBuilderValidationSelectColumn: '列を選択する必要があります。',
    advancedFilterBuilderValidationSelectOption: 'オプションを選択する必要があります。',
    advancedFilterContains: '含む',
    advancedFilterEndsWith: 'で終わる',
    advancedFilterEquals: '=',
    advancedFilterFalse: '偽',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterNotBlank: '空白でない',
    advancedFilterNotContains: '含まない',
    advancedFilterNotEqual: '!=',
    advancedFilterOr: 'または',
    advancedFilterStartsWith: 'で始まる',
    advancedFilterTextEquals: '等しい',
    advancedFilterTextNotEqual: '等しくない',
    advancedFilterTrue: '真',
    advancedFilterValidationExtraEndBracket: '終端の括弧が多すぎます',
    advancedFilterValidationInvalidColumn: '列が見つかりません',
    advancedFilterValidationInvalidDate: '値が有効な日付ではありません',
    advancedFilterValidationInvalidJoinOperator: '結合演算子が見つかりません',
    advancedFilterValidationInvalidOption: 'オプションが見つかりません',
    advancedFilterValidationJoinOperatorMismatch: '条件内の結合演算子は同じである必要があります',
    advancedFilterValidationMessage: '式にエラーがあります。${variable} - ${variable}。',
    advancedFilterValidationMessageAtEnd: '式にエラーがあります。式の最後に${variable}。',
    advancedFilterValidationMissingColumn: '列が欠落しています',
    advancedFilterValidationMissingCondition: '条件が欠落しています',
    advancedFilterValidationMissingEndBracket: '終端の括弧が欠落しています',
    advancedFilterValidationMissingOption: 'オプションが欠落しています',
    advancedFilterValidationMissingQuote: '値の終端の引用符が欠落しています',
    advancedFilterValidationMissingValue: '値が欠落しています',
    advancedFilterValidationNotANumber: '値が数値ではありません',
    advancedSettings: '詳細設定',
    after: '後',
    aggregate: '集計',
    andCondition: 'AND',
    animation: 'アニメーション',
    applyFilter: '適用',
    april: '4月',
    area: 'エリア',
    areaChart: 'エリア',
    areaColumnComboTooltip: 'エリア＆カラム',
    areaGroup: 'エリア',
    ariaAdvancedFilterBuilderColumn: '列',
    ariaAdvancedFilterBuilderFilterItem: 'フィルター条件',
    ariaAdvancedFilterBuilderGroupItem: 'フィルターグループ',
    ariaAdvancedFilterBuilderItem:
      '${variable}。 レベル ${variable}。 編集するにはENTERを押してください。',
    ariaAdvancedFilterBuilderItemValidation:
      '${variable}。 レベル ${variable}。 ${variable} 編集するにはENTERを押してください。',
    ariaAdvancedFilterBuilderJoinOperator: '結合演算子',
    ariaAdvancedFilterBuilderList: '高度なフィルタービルダリスト',
    ariaAdvancedFilterBuilderOption: 'オプション',
    ariaAdvancedFilterBuilderValueP: '値',
    ariaAdvancedFilterInput: '高度なフィルター入力',
    ariaChartMenuClose: 'チャート編集メニューを閉じる',
    ariaChartSelected: '選択済み',
    ariaChecked: 'チェック済み',
    ariaColumn: '列',
    ariaColumnFiltered: 'フィルターが適用された列',
    ariaColumnGroup: '列グループ',
    ariaColumnPanelList: '列リスト',
    ariaColumnSelectAll: 'すべての列を選択/選択解除',
    ariaDateFilterInput: '日付フィルター入力',
    ariaDefaultListName: 'リスト',
    ariaDropZoneColumnComponentAggFuncSeparator: ' の ',
    ariaDropZoneColumnComponentDescription: '削除するには DELETE を押してください',
    ariaDropZoneColumnComponentSortAscending: '昇順',
    ariaDropZoneColumnComponentSortDescending: '降順',
    ariaDropZoneColumnGroupItemDescription: 'ソートするには ENTER を押してください',
    ariaDropZoneColumnValueItemDescription: '集計タイプを変更するには ENTER を押してください',
    ariaFilterColumn: 'フィルターを開くにはCTRL+ENTERを押してください',
    ariaFilterColumnsInput: 'フィルター列入力',
    ariaFilterFromValue: '値からフィルター',
    ariaFilterInput: 'フィルター入力',
    ariaFilterList: 'フィルターリスト',
    ariaFilterMenuOpen: 'フィルターメニューを開く',
    ariaFilterPanelList: 'フィルターリスト',
    ariaFilterToValue: '値までフィルター',
    ariaFilterValue: 'フィルター値',
    ariaFilteringOperator: 'フィルター演算子',
    ariaHidden: '非表示',
    ariaIndeterminate: '不確定',
    ariaInputEditor: '入力エディター',
    ariaLabelAdvancedFilterAutocomplete: '高度なフィルターオートコンプリート',
    ariaLabelAdvancedFilterBuilderAddField: '高度なフィルタービルダーにフィールドを追加',
    ariaLabelAdvancedFilterBuilderColumnSelectField: '高度なフィルタービルダーの列選択フィールド',
    ariaLabelAdvancedFilterBuilderJoinSelectField:
      '高度なフィルタービルダーの結合演算子選択フィールド',
    ariaLabelAdvancedFilterBuilderOptionSelectField:
      '高度なフィルタービルダーのオプション選択フィールド',
    ariaLabelAggregationFunction: '集計関数',
    ariaLabelCellEditor: 'セルエディター',
    ariaLabelColumnFilter: '列フィルター',
    ariaLabelColumnMenu: '列メニュー',
    ariaLabelContextMenu: 'コンテキストメニュー',
    ariaLabelDialog: 'ダイアログ',
    ariaLabelRichSelectDeleteSelection: 'アイテムの選択を解除するにはDELETEを押してください',
    ariaLabelRichSelectDeselectAllItems:
      'すべてのアイテムの選択を解除するにはDELETEを押してください',
    ariaLabelRichSelectField: 'リッチセレクトフィールド',
    ariaLabelRichSelectToggleSelection: '選択を切り替えるにはスペースを押してください',
    ariaLabelSelectField: 'フィールドを選択',
    ariaLabelSubMenu: 'サブメニュー',
    ariaLabelTooltip: 'ツールチップ',
    ariaMenuColumn: '列メニューを開くにはALT+↓を押してください',
    ariaPageSizeSelectorLabel: 'ページサイズ',
    ariaPivotDropZonePanelLabel: '列ラベル',
    ariaRowDeselect: 'この行の選択を解除するにはSPACEを押してください',
    ariaRowGroupDropZonePanelLabel: '行グループ',
    ariaRowSelect: 'この行を選択するにはSPACEを押してください',
    ariaRowSelectAll: 'すべての行の選択を切り替えるにはSPACEを押してください',
    ariaRowSelectionDisabled: 'この行の選択は無効です',
    ariaRowToggleSelection: '行の選択を切り替えるにはSPACEを押してください',
    ariaSearch: '検索',
    ariaSearchFilterValues: 'フィルター値を検索',
    ariaSkeletonCellLoading: '行データを読み込み中',
    ariaSkeletonCellLoadingFailed: '行の読み込みに失敗しました',
    ariaSortableColumn: '並べ替えるにはENTERを押してください',
    ariaToggleCellValue: 'セルの値を切り替えるにはSPACEを押してください',
    ariaToggleVisibility: '表示を切り替えるにはSPACEを押してください',
    ariaUnchecked: '未チェック',
    ariaValuesDropZonePanelLabel: '値',
    ariaVisible: '表示',
    asc_option: 'オプションで昇順',
    august: '8月',
    autoRotate: '自動回転',
    automatic: '自動',
    autosizeAllColumns: 'すべての列の自動サイズ調整',
    autosizeThisColumn: 'この列の自動サイズ調整',
    avg: '平均',
    axis: '軸',
    axisType: '軸タイプ',
    background: '背景',
    bar: 'バー',
    barChart: 'バー',
    barGroup: 'バー',
    before: '前',
    blank: '空白',
    blanks: '(空白)',
    blur: 'ぼかし',
    bold: '太字',
    boldItalic: '太字イタリック',
    bottom: '下',
    boxPlot: 'ボックスプロット',
    boxPlotTooltip: 'ボックスプロット',
    bubble: 'バブル',
    bubbleTooltip: 'バブル',
    callout: 'コールアウト',
    calloutLabels: 'コールアウトラベル',
    cancelFilter: 'キャンセル',
    cap: 'キャップ',
    capLengthRatio: '長さ比率',
    categories: 'カテゴリー',
    category: 'カテゴリー',
    categoryAdd: 'カテゴリを追加',
    categoryValues: 'カテゴリー値',
    chartAdvancedSettings: '詳細設定',
    chartDownload: 'チャートをダウンロード',
    chartDownloadToolbarTooltip: 'チャートをダウンロード',
    chartEdit: 'チャートを編集',
    chartLink: 'グリッドにリンク',
    chartLinkToolbarTooltip: 'グリッドにリンク',
    chartMenuToolbarTooltip: 'メニュー',
    chartRange: 'チャート範囲',
    chartSettingsToolbarTooltip: 'メニュー',
    chartStyle: 'チャートスタイル',
    chartSubtitle: 'サブタイトル',
    chartTitle: 'チャートタイトル',
    chartTitles: 'タイトル',
    chartUnlink: 'グリッドからリンク解除',
    chartUnlinkToolbarTooltip: 'グリッドからリンク解除',
    circle: '円',
    clearFilter: 'クリア',
    collapseAll: 'すべての行グループを閉じる',
    color: '色',
    column: 'カラム',
    columnChart: 'カラム',
    columnChooser: '列の選択',
    columnFilter: '列フィルター',
    columnGroup: 'カラム',
    columnLineCombo: 'カラムとライン',
    columnLineComboTooltip: 'カラム＆ライン',
    columns: '列',
    combinationChart: 'コンビネーション',
    combinationGroup: 'コンビネーション',
    connectorLine: 'コネクター線',
    contains: '含む',
    copy: 'コピー',
    copyWithGroupHeaders: 'グループヘッダー付きでコピー',
    copyWithHeaders: 'ヘッダー付きでコピー',
    copy_row: '行をコピー',
    count: 'カウント',
    cross: 'クロス',
    crosshair: 'クロスヘア',
    crosshairLabel: 'ラベル',
    crosshairSnap: 'ノードにスナップ',
    csvExport: 'CSVエクスポート',
    ctrlC: 'Ctrl+C',
    ctrlV: 'Ctrl+V',
    ctrlX: 'Ctrl+X',
    customCombo: 'カスタムコンビネーション',
    customComboTooltip: 'カスタムコンビネーション',
    cut: 'カット',
    data: '設定',
    dateFilter: '日付フィルター',
    dateFormatOoo: 'yyyy-mm-dd',
    december: '12月',
    decimalSeparator: '。',
    defaultCategory: '(なし)',
    desc_option: 'オプションで降順',
    diamond: 'ダイヤモンド',
    direction: '方向',
    donut: 'ドーナツ',
    donutTooltip: 'ドーナツ',
    duplicate_record: '重複するレコード',
    durationMillis: '期間 (ms)',
    empty: '選択してください',
    enabled: '有効',
    endAngle: '終了角度',
    endsWith: 'で終わる',
    equals: '等しい',
    excelExport: 'Excelエクスポート',
    expandAll: 'すべての行グループを展開',
    expand_record: 'レコードを展開',
    export: 'エクスポート',
    false: '偽',
    february: '2月',
    fillOpacity: '塗りの不透明度',
    filterOoo: 'フィルター...',
    filteredRows: 'フィルタ済み',
    filters: 'フィルター',
    first: '最初',
    firstPage: '最初のページ',
    fixed: '固定',
    font: 'フォント',
    footerTotal: '合計',
    format: 'カスタマイズ',
    greaterThan: 'より大きい',
    greaterThanOrEqual: '以上',
    gridLines: 'グリッド線',
    group: 'グループ',
    groupBy: 'グループ化',
    groupFilterSelect: 'フィールドを選択:',
    groupPadding: 'グループパディング',
    groupedAreaTooltip: 'エリア',
    groupedBar: 'グループ化',
    groupedBarFull: 'グループ化バー',
    groupedBarTooltip: 'グループ化',
    groupedColumn: 'グループ化',
    groupedColumnFull: 'グループ化カラム',
    groupedColumnTooltip: 'グループ化',
    groupedSeriesGroupType: 'グループ化',
    groups: '行グループ',
    heart: 'ハート',
    heatmap: 'ヒートマップ',
    heatmapTooltip: 'ヒートマップ',
    height: '高さ',
    hierarchicalChart: '階層',
    hierarchicalGroup: '階層',
    histogram: 'ヒストグラム',
    histogramBinCount: 'ビンの数',
    histogramChart: 'ヒストグラム',
    histogramFrequency: '頻度',
    histogramTooltip: 'ヒストグラム',
    horizontal: '水平',
    horizontalAxisTitle: '水平軸タイトル',
    inRange: '間',
    inRangeEnd: 'まで',
    inRangeStart: 'から',
    innerRadius: '内側半径',
    inside: '内側',
    invalidColor: '色の値が無効です',
    invalidDate: '無効な日付',
    invalidNumber: '無効な数値',
    italic: 'イタリック',
    itemPaddingX: '項目パディングX',
    itemPaddingY: '項目パディングY',
    itemSpacing: '項目間隔',
    january: '1月',
    july: '7月',
    june: '6月',
    labelPlacement: '配置',
    labelRotation: '回転',
    labels: 'ラベル',
    last: '最後',
    lastPage: '最後のページ',
    layoutHorizontalSpacing: '水平間隔',
    layoutVerticalSpacing: '垂直間隔',
    left: '左',
    legend: '凡例',
    legendEnabled: '有効',
    length: '長さ',
    lessThan: '未満',
    lessThanOrEqual: '以下',
    line: 'ライン',
    lineDash: '線の破線',
    lineDashOffset: '破線オフセット',
    lineGroup: 'ライン',
    lineTooltip: 'ライン',
    lineWidth: '線の幅',
    loadingError: 'エラー',
    loadingOoo: '読み込み中...',
    lookup_unamed_record: '無名の記録',
    march: '3月',
    markerPadding: 'マーカーパディング',
    markerSize: 'マーカーサイズ',
    markerStroke: 'マーカーストローク',
    markers: 'マーカー',
    max: '最大',
    maxSize: '最大サイズ',
    may: '5月',
    min: '最小',
    minSize: '最小サイズ',
    miniChart: 'ミニチャート',
    more: 'もっと',
    navigator: 'ナビゲーター',
    nextPage: '次のページ',
    nightingale: 'ナイチンゲール',
    nightingaleTooltip: 'ナイチンゲール',
    noAggregation: 'なし',
    noDataToChart: 'チャートにするデータがありません。',
    noMatches: '一致するものがありません',
    noPin: '固定なし',
    noRowsToShow: '表示する行がありません',
    none: 'なし',
    normal: '普通',
    normalizedArea: '100% 積み重ね',
    normalizedAreaFull: '100%積み上げエリア',
    normalizedAreaTooltip: '100%積み上げ',
    normalizedBar: '100% 積み重ね',
    normalizedBarFull: '100%積み上げバー',
    normalizedBarTooltip: '100%積み上げ',
    normalizedColumn: '100% 積み重ね',
    normalizedColumnFull: '100%積み上げカラム',
    normalizedColumnTooltip: '100%積み上げ',
    normalizedSeriesGroupType: '100%積み上げ',
    notBlank: '空白ではない',
    notContains: '含まない',
    notEqual: '等しくない',
    november: '11月',
    number: '数値',
    numberFilter: '数値フィルター',
    october: '10月',
    of: 'の',
    offset: 'オフセット',
    offsets: 'オフセット',
    orCondition: 'OR',
    orientation: '方向',
    outside: '外側',
    padding: 'パディング',
    page: 'ページ',
    pageLastRowUnknown: '?',
    pageSizeSelectorLabel: 'ページサイズ：',
    paired: 'ペアモード',
    parallel: '平行',
    paste: 'ペースト',
    pasting_multiple_columns_is_not_supportted_currently:
      '複数列の貼り付けは現在サポートされていません',
    perpendicular: '垂直',
    pie: 'パイ',
    pieChart: 'パイ',
    pieGroup: 'パイ',
    pieTooltip: 'パイ',
    pinColumn: '列の固定',
    pinLeft: '左に固定',
    pinRight: '右に固定',
    pivotChart: 'ピボットチャート',
    pivotChartAndPivotMode: 'ピボットチャートとピボットモード',
    pivotChartRequiresPivotMode: 'ピボットチャートにはピボットモードが必要です。',
    pivotChartTitle: 'ピボットチャート',
    pivotColumnGroupTotals: '合計',
    pivotColumnsEmptyMessage: 'ここにドラッグして列ラベルを設定します',
    pivotMode: 'ピボットモード',
    pivots: '列ラベル',
    plus: 'プラス',
    polarAxis: '極軸',
    polarAxisTitle: '極軸タイトル',
    polarChart: '極座標',
    polarGroup: '極',
    polygon: '多角形',
    position: '位置',
    positionRatio: '位置比率',
    predefined: '定義済み',
    preferredLength: '推奨長さ',
    previousPage: '前のページ',
    radarArea: 'レーダーエリア',
    radarAreaTooltip: 'レーダーエリア',
    radarLine: 'レーダーライン',
    radarLineTooltip: 'レーダーライン',
    radialBar: '放射状バー',
    radialBarTooltip: 'ラジアルバー',
    radialColumn: '放射状カラム',
    radialColumnTooltip: 'ラジアルカラム',
    radiusAxis: '半径軸',
    radiusAxisPosition: '位置',
    rangeArea: 'レンジエリア',
    rangeAreaTooltip: '範囲エリア',
    rangeBar: 'レンジバー',
    rangeBarTooltip: '範囲バー',
    rangeChartTitle: '範囲チャート',
    removeFromLabels: '${variable}をラベルから削除',
    removeFromValues: '${variable}を値から削除',
    resetColumns: '列をリセット',
    resetFilter: 'リセット',
    reverseDirection: '逆方向',
    right: '右',
    rowDragRow: '行',
    rowDragRows: '行',
    rowGroupColumnsEmptyMessage: 'ここにドラッグして行グループを設定します',
    row_group: 'グループ分け',
    scatter: '散布図',
    scatterGroup: 'X Y (散布図)',
    scatterTooltip: '散布図',
    scrollingStep: 'スクロールステップ',
    scrollingZoom: 'スクロール',
    searchOoo: '検索...',
    secondaryAxis: '第二軸',
    sectorLabels: 'セクターラベル',
    see_more_detail: 'もっと見る',
    selectAll: '(すべて選択)',
    selectAllSearchResults: '(検索結果をすべて選択)',
    selectedRows: '選択済み',
    selectingZoom: '選択',
    september: '9月',
    series: 'シリーズ',
    seriesAdd: 'シリーズを追加',
    seriesChartType: 'シリーズチャートタイプ',
    seriesGroupType: 'グループタイプ',
    seriesItemLabels: 'アイテムラベル',
    seriesItemNegative: 'ネガティブ',
    seriesItemPositive: 'ポジティブ',
    seriesItemType: 'アイテムタイプ',
    seriesItems: 'シリーズアイテム',
    seriesLabels: 'シリーズラベル',
    seriesPadding: 'シリーズパディング',
    seriesType: 'シリーズタイプ',
    setFilter: 'セットフィルター',
    settings: 'チャート',
    shadow: '影',
    shape: '形状',
    size: 'サイズ',
    sortAscending: '昇順で並べ替え',
    sortDescending: '降順で並べ替え',
    sortUnSort: 'ソート解除',
    spacing: '間隔',
    specializedChart: '特殊',
    specializedGroup: '専門',
    square: '四角形',
    stackedArea: '積み重ね',
    stackedAreaFull: '積み上げエリア',
    stackedAreaTooltip: '積み上げ',
    stackedBar: '積み重ね',
    stackedBarFull: '積み上げバー',
    stackedBarTooltip: '積み上げ',
    stackedColumn: '積み重ね',
    stackedColumnFull: '積み上げカラム',
    stackedColumnTooltip: '積み上げ',
    stackedSeriesGroupType: '積み上げ',
    startAngle: '開始角度',
    startsWith: 'で始まる',
    statisticalChart: '統計',
    statisticalGroup: '統計',
    strokeColor: '線の色',
    strokeOpacity: '線の不透明度',
    strokeWidth: 'ストローク幅',
    sum: '合計',
    sunburst: 'サンバースト',
    sunburstTooltip: 'サンバースト',
    switchCategorySeries: 'カテゴリー/シリーズの切り替え',
    textFilter: 'テキストフィルター',
    thickness: '厚さ',
    thousandSeparator: '、',
    ticks: '目盛',
    tile: 'タイル',
    time: '時間',
    timeFormat: '時間形式',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS 午前/午後',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    title: 'タイトル',
    titlePlaceholder: 'チャートタイトル',
    to: 'から',
    tooltips: 'ツールチップ',
    top: '上',
    totalAndFilteredRows: '行',
    totalRows: '総行数',
    treemap: 'ツリーマップ',
    treemapTooltip: 'ツリーマップ',
    triangle: '三角形',
    true: '真',
    ungroupAll: 'すべてのグループ化を解除',
    ungroupBy: 'グループ化解除',
    valueAggregation: '値の集計',
    valueColumnsEmptyMessage: 'ここにドラッグして集計します',
    values: '値',
    vertical: '垂直',
    verticalAxisTitle: '垂直軸タイトル',
    waterfall: 'ウォーターフォール',
    waterfallTooltip: 'ウォーターフォール',
    weight: '太さ',
    whisker: 'ウィスカー',
    width: '幅',
    xAxis: '水平軸',
    xOffset: 'Xオフセット',
    xType: 'Xタイプ',
    xyChart: 'X Y (散布図)',
    xyValues: 'X Y値',
    yAxis: '垂直軸',
    yOffset: 'Yオフセット',
    zoom: 'ズーム',
  },
  agenda: {
    agenda: '予定',
    description: '説明',
    reminder_time: 'リマインダー時間',
  },
  ai: {
    ai_image_generation: 'AI画像生成',
    ai_models_feature: 'AI Models',
    ai_models_feature_description:
      'Bika.ai comes with multiple preset AI models, including the latest offerings from OpenAI, Anthropic, and more, as well as integrated text-to-text, text-to-image, and text-to-video models.\n\nWithin AI Agents, Automation, and AI Database Fields, you can choose from these preset models according to your needs. Preset models are optimized and fine-tuned by our team to deliver the highest performance, with credit consumption varying depending on the model tier.\n\nIn certain versions, you can even customize your own AI models by configuring different AI model providers and API key tokens. This allows you to minimize credit usage while tailoring models to your specific requirements.',
    ai_models_features_list: 'AI Models List',
    ai_translate_all: 'すべて翻訳',
    artifact_code: 'コード',
    artifact_outline: 'アウトライン',
    artifact_preview: '預覧',
    artifact_template_code: 'コード',
    artifact_workflow: 'ワークフロー',
    completed: '完了',
    generate: '生成',
    generate_image: '画像を生成',
    generated_characters: '生成された{characters}文字、完了{percent}%',
    generated_result: '生成結果',
    generating: '生成中...',
    history: '履歴',
    in_progress: '進行中',
    insert: 'コンテンツを挿入',
    launcher_esc: '閉じる',
    launcher_open: '開く',
    launcher_select: '選択',
    launcher_tips_prefix: '使用する',
    launcher_tips_suffix: 'フィルターを切り替える',
    load_more: 'Load More',
    name: 'AI ライティング',
    new_chat: '新規チャット',
    overwrite: 'コンテンツを置き換える',
    pick_an_image: '画像を選択',
    press_enter: 'Enterで送信、Shift+Enterで改行',
    reference: '参考資料 {count}点',
    regenerate: '再生成',
    restore_conversation: '会話を復元する',
    share_conversation: '会話を共有する',
    slide_page_number: '{number} ページ目',
    slide_retry_count: '{count}/{max} 回目の試行',
    slides_completed: '完了',
    slides_generating: 'スライドを生成中...',
    slides_generation_failed: 'スライド生成が複数回の再試行後に失敗しました',
    slides_pending: '開始待ち...',
    slides_status_failed: '生成失敗',
    slides_status_generating: '生成中...',
    start_chat: 'チャットを開始',
    thinking: '考え中...',
    thought: '{seconds}秒間考えました',
    type_message: 'メッセージを入力してください',
    voice_hold_to_speak: '話すときは長押し',
    voice_release_to_send: '話し終わったら離して送信',
  },
  ai_consultant: {
    deep_think: '深く考える',
    description:
      '私たちはあなたのAIビジネスコンサルタントです。ビジネスの分析、ソリューション提案の作成、自動化AIシステムの構築をお手伝いします',
    history: '履歴',
    quick: '標準',
    replay: 'リプレイ',
    title: 'AI アプリケーションアドバイザー',
  },
  api: {
    api: 'API',
    create_token: 'トークンを作成',
    create_token_description:
      'これらのトークンは、他のアプリがあなたのアカウント全体を制御することを許可します。注意してください！',
    create_token_success: 'トークンの作成に成功しました',
    created_public_api_token: '公開APIトークンを作成しました',
    delete_token: 'トークンを削除',
    delete_token_description: 'このトークンを削除してもよろしいですか？',
    delete_token_success: 'トークンの削除に成功しました',
    developer_api: '開発者API',
    e180days: '180日間',
    e1day: '1日間',
    e1month: '1ヶ月',
    e1year: '1年間',
    e2month: '2ヶ月',
    e30days: '30日間',
    e3days: '3日間',
    e6month: '6ヶ月',
    e7days: '7日間',
    expiration: '有効期限',
    my_tokens: 'マイ トークン',
    my_tokens_description: 'API トークンは他のパスワードと同様に安全に扱う必要があります。',
    name: '名前',
    never: '無期限',
    select_expiration: '有効期限を選択',
  },
  auth: {
    agree_description:
      'Bika.aiをご利用いただきありがとうございます。以下の条項は、ユーザーおよびプライバシーポリシーに関する情報を提供し、関連する権利を理解するのに役立ちます。完全な内容は、{privacy} と {team} でご確認いただけます。',
    agree_title: '利用規約をお読みいただき、同意してください',
    auth_error: '認証エラー',
    auth_error_description: '認証エラーが発生しました。もう一度お試しください。',
    back: '戻る',
    back_website: 'ホームページに戻る',
    continue_with_apple: 'Appleで続ける',
    continue_with_email: 'メールで続ける',
    continue_with_facebook: 'Facebookで続ける',
    continue_with_github: 'GitHubで続ける',
    continue_with_google: 'Googleで続ける',
    continue_with_phone: '電話番号で続ける',
    continue_with_twitter: 'Twitterで続ける',
    continue_with_username: 'アカウント名で続ける',
    continue_with_weixin: 'WeChatで続ける',
    get_started: 'はじめる',
    invite_you_to_join: 'あなたを招待します',
    invite_you_to_register: '{me} があなたをBika.aiに招待しました',
    link_account_already_exists:
      '{type}アカウントは既に別のBikaアカウントにバインドされています。バインドされたアカウントに直接ログインしますか？',
    loading: '読み込み中...',
    login: 'ログイン',
    login_and_register: 'ログイン/登録',
    login_success: 'ログイン成功',
    logout: 'ログアウト',
    no_permission:
      '申し訳ありませんが、この招待リンクは企業内部のユーザーのみ利用可能です。企業のメールアドレスでログインするか、管理者に連絡して有効な招待リンクを取得してください',
    or: 'または',
    password: 'パスワード',
    password_is_required: 'パスワードが必要です。',
    privacy_policy: 'プライバシーポリシー',
    quick_login: 'クイックログイン',
    quick_login_description: 'パスワードと認証なしで新しいユーザーをすばやく登録します。',
    register_agreement: 'アカウント作成により{team}と{privacy}に同意します。',
    scan_qrcode_to_login: 'QRコードをスキャンしてログイン',
    sign_in: 'サインイン',
    sign_up: 'サインアップ',
    switch_account: 'アカウントを切り替える',
    terms_and_conditions:
      'プライバシーポリシーと利用規約をお読みになり、同意してください。プライバシーとデータの保護に取り組んでいます。',
    terms_of_service: '利用規約',
  },
  automation: {
    action: {
      actions: 'アクション',
      ai_summary: {
        description: 'AIモデルを使用してテキストコンテンツを要約します。',
        name: 'AIテキスト要約',
      },
      call_agent: {
        agent_id_subject: 'エージェント ID',
        description: '指定されたエージェントにメッセージを送信します。',
        message_subject: 'メッセージ',
        name: 'エージェントに電話をかける',
        recipient: '受信者',
      },
      condition: {
        description: '条件の結果に基づいて異なるアクションを実行します。',
        name: '条件',
      },
      create_document: {
        description: '複数のステップと変数の組み合わせを通じて、ドキュメントを動的に生成します。',
        name: 'ドキュメントを作成',
      },
      create_mission: {
        description:
          '新しいミッションを作成し、指定されたメンバー、役割、またはチームに割り当てます。',
        name: 'ミッションを作成',
      },
      create_node_resource: {
        description:
          '複数のステップと変数の組み合わせを通じて、リソースノード（データベース、ドキュメント、オートメーション、フォームなど）を動的に生成します。',
        name: 'リソースノードを作成',
      },
      create_record: {
        description: '指定されたデータベースに新しいレコードを作成します。',
        name: 'レコードを作成',
      },
      deepseek_generate_text: {
        description: 'DeepSeek API を使用してテキストコンテンツを生成します。',
        name: 'DeepSeek - テキスト生成',
      },
      delay: {
        description: '次のアクションを実行する前に一定時間一時停止します。',
        name: '遅延',
        queue: 'キュー',
        unit: '単位',
        unit_day: '日',
        unit_hour: '時間',
        unit_minute: '分',
        unit_second: '秒',
        unit_week: '週',
        value: '値',
      },
      description: '備考',
      dingtalk_webhook: {
        description:
          'DingTalkカスタムロボットのWebhook URLを介して、指定されたグループにメッセージを送信します。',
        message_title_description:
          'メッセージのタイトルを入力してください。このタイトルは、ディンドウの左側のメッセージリストの要約に表示されます。',
        name: 'DingTalkグループにメッセージを送信',
      },
      dummy_action: {
        description: '自動化プロセスアクションのテストおよび検証に使用されます。',
        name: 'ダミーアクション',
      },
      feishu_webhook: {
        description:
          '飛書カスタムロボットのWebhook URLを介して、指定されたグループにメッセージを送信します。',
        name: '飛書グループにメッセージを送信',
      },
      filter: {
        description: '選択条件を満たしている場合、後続の実行プログラムを実行します。',
        name: 'フィルター',
      },
      find_dashboard: {
        description: '指定されたダッシュボードを検索します。',
        name: 'ダッシュボードを検索',
      },
      find_members: {
        description: '特定の基準を満たす宇宙ステーションのメンバーを検索します。',
        name: 'メンバーリストを取得',
        to_email_addresses: 'メールアドレス',
        to_email_addresses_description:
          '複数のメールアドレスはコンマで区切り、"/"を入力すると変数を挿入できます',
      },
      find_missions: {
        description: '指定されたミッションを検索します。',
        name: 'ミッションを検索',
      },
      find_records: {
        description:
          '指定されたデータベースから、ビューやフィルタリング基準に基づいて複数のレコードを取得します。',
        find_database: 'ビューに基づいてフィルタリング',
        find_database_select: 'データテーブルに基づいてフィルタリング',
        name: 'レコードの取得',
        placeholder_select_type: '検索方法を選択してください',
        records_limit_description: '検索毎の最大レコード数。1に設定時は単一レコードのみ返却。',
        records_limit_placeholder: '1 - 100',
        records_limit_title: '検索レコード制限',
        title_interrupt_if_no_record: 'レコードが見つからない場合、次のアクションを中断',
        type: '検索方法',
      },
      find_widget: {
        description: '指定されたウィジェットを検索します。',
        name: 'ウィジェットを検索',
        widget_empty: 'ウィジェットは空にできません',
      },
      formapp_ai: {
        create_and_load_formapp: 'フォームアプリを作成して読み込む',
        description:
          'FormApp.aiは、ワークフローを自動化するための多数のAIモデル、アクション、拡張機能を提供します。また、独自のAIモデル、アクション、APIをカスタマイズすることもできます。',
        name: 'FormApp.ai',
      },
      loop: {
        abort_loop_help:
          '子アクションでエラーが発生すると即座にループ全体が中止され、残りの反復処理は実行されません。',
        abort_loop_title: '失敗時にループを中止',
        add_action: 'アクションを追加',
        child_actions: '子アクション',
        child_actions_help: '各ループ反復内で順番に実行されるアクション。',
        description:
          'データセットを反復処理し、事前設定された条件に基づいて対応するアクションをトリガーすることで、反復的なタスクを自動化するために使用されます。',
        name: 'ループ',
        retrieve: '上流ステップから配列データを選択',
        retrieve_description:
          '上流ステップの出力データを選択して反復処理を行います。配列型データのみサポートします。',
        sequential_exec: '順次実行',
        sequential_exec_help:
          '配列要素を厳密なインデックス順で処理し、次の反復を開始する前に現在の反復を完了します。並列処理を有効にするにはチェックを外してください。',
      },
      not_found: 'アクションが見つかりません',
      openai_generate_text: {
        apikey: 'API キー',
        description: 'OpenAI API を使用してテキストコンテンツを生成します。',
        model: 'モデル',
        name: 'OpenAI - テキスト作成',
        placeholder: 'モデルを選択してください',
        prompt: 'プロンプト (Prompt)',
        timeout_label: 'タイムアウト（秒）',
        timeout_placeholder:
          'タイムアウト時間を入力してください。最大値は300秒で、デフォルトは60秒です。',
      },
      random: {
        description: '複数の入力からランダムに一つのオプションを出力として選択します。',
        name: 'ランダム',
      },
      replace_file: {
        description: 'データテーブルのレコードを使用してファイルを一括置換します。',
        name: 'ファイルを置き換える',
      },
      round_robin: {
        description: '複数の入力を順番に循環させて出力とします。',
        name: 'ラウンドロビン',
      },
      run_script: {
        description:
          'カスタム操作を実行するためのスクリプトコードを記述します。Python、JavaScriptをサポートします。',
        name: 'スクリプトを実行',
      },
      send_email: {
        description:
          '指定された受信者にメールを送信します。一斉送信、カスタムSMTPなどの機能をサポートします。',
        name: 'メールを送信',
        opens: '閲覧率',
        sent: '送信数',
      },
      send_report: {
        description: 'レポートを生成し、指定されたメンバーやグループに送信します。',
        name: 'レポートを送信',
      },
      slack_webhook: {
        description:
          'SlackアプリのIncoming Webhookを介して、指定されたチャンネルにメッセージを送信します。',
        name: 'Slackチャンネルにメッセージを送信',
      },
      telegram_send_message: {
        description:
          'Telegram Botを介して、指定されたユーザーまたはグループにメッセージを送信します。',
        help_text_chat_id:
          'パブリックチャンネルのチャットIDは「@」で始める必要があります。例: @channel_name',
        name: 'Telegramにメッセージを送信',
      },
      toolsdk_ai: {
        create_and_load_toolsdk: 'ToolSDKを作成して読み込む',
        description:
          'ToolSDK.ai は 2000+ の MCP サーバーと 10000+ の AI ツールを提供しています。自動化プロセス内でサードパーティの AI 機能を簡単に使用し、ワークフロー機能を迅速に拡張することができます。',
        name: 'MCP Server (by ToolSDK.ai)',
      },
      twitter_upload_media: {
        description:
          'X(Twitter) API を介してメディアファイルをアップロードし、画像とビデオをサポートします。',
        media: 'メディアURL',
        name: 'X(Twitter) - メディアをアップロード',
      },
      type: 'アクションタイプ',
      update_record: {
        description: '指定されたデータベース内の一つまたは複数のレコードを更新します。',
        name: 'レコードを更新',
      },
      webhook: {
        description: '他のシステムとのデータ交換のためにHTTPリクエストを開始します。',
        edit: 'リクエストボディを編集',
        name: 'HTTPリクエストを送信',
      },
      wecom_webhook: {
        description:
          'WeComグループロボットのWebhook URLを介して、指定されたグループにメッセージを送信します。',
        name: 'WeComグループにメッセージを送信',
      },
      x_create_tweet: {
        auth_method: '認証方法',
        auth_method_help_text:
          '異なる認証方法には異なる統合インスタンスが必要です。認証方法を変更した後、統合を再選択してください。',
        auth_method_tooltip: 'OAuth 1.0aとOAuth 2.0の違いについて詳しく知る',
        description: 'X(Twitter) APIを介してツイートを公開します。',
        media_ids: 'メディア',
        media_ids_help_text:
          'ツイートに画像や動画を追加するには、「X(Twitter) - メディアをアップロード」アクションを使用してメディアIDを取得し、変数セレクターを使用してここに挿入してください。',
        name: 'X(Twitter) - ツイートを作成',
        tweet_content: 'ツイート内容',
        x_account_integration: 'X(Twitter)アカウント統合',
      },
    },
    action_help_urls: {
      DINGTALK_WEBHOOK: '/help/reference/automation-action/dingtalk-webhook',
      DINGTALK_WEBHOOK_MARKDOWN: 'https://open.dingtalk.com/document/isvapp/message-type',
      FEISHU_WEBHOOK: '/help/reference/automation-action/feishu-webhook',
      FEISHU_WEBHOOK_MARKDOWN:
        'https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot#5a997364',
      SEND_EMAIL: '/help/reference/integration/smtp-email-account',
      SLACK_WEBHOOK: '/help/reference/automation-action/slack-webhook',
      SLACK_WEBHOOK_MARKDOWN: 'https://api.slack.com/reference/surfaces/formatting',
      TELEGRAM_SEND_MESSAGE: '/help/reference/automation-action/telegram-send-message',
      TELEGRAM_SEND_MESSAGE_MARKDOWN: 'https://core.telegram.org/bots/api#formatting-options',
      TWITTER_UPLOAD_MEDIA: '/help/reference/automation-action/twitter-upload-media',
      WEBHOOK: '/help/reference/automation/webhook-action',
      WECOM_WEBHOOK: '/help/reference/automation-action/wecom-webhook',
      WECOM_WEBHOOK_MARKDOWN:
        'https://developer.work.weixin.qq.com/document/path/91770#markdown%E7%B1%BB%E5%9E%8B',
      WECOM_WEBHOOK_TEMPLATE_CARD:
        'https://developer.work.weixin.qq.com/document/path/91770#%E6%A8%A1%E7%89%88%E5%8D%A1%E7%89%87%E7%B1%BB%E5%9E%8B',
      X_CREATE_TWEET: '/help/reference/automation-action/x-create-tweet',
    },
    action_type_intro: {
      DINGTALK_WEBHOOK:
        'DingTalkカスタムボットのWebhook URLを通じて、指定されたグループにメッセージを送信します。',
      FEISHU_WEBHOOK:
        'FeishuカスタムボットのWebhook URLを通じて、指定されたグループにメッセージを送信します。',
      SLACK_WEBHOOK:
        'SlackアプリのIncoming Webhookを通じて、指定されたチャンネルにメッセージを送信します。',
      TELEGRAM_SEND_MESSAGE:
        'Telegramボットを通じて、指定されたユーザーまたはグループにメッセージを送信します。',
      WEBHOOK: 'カスタムHTTPリクエストを構築し、指定されたURLに送信します。',
      WECOM_WEBHOOK:
        'WeComグループチャットのWebhook URLを通じて、指定されたグループにメッセージを送信します。',
      X_CREATE_TWEET: 'Twitter APIを通じて、ツイートを投稿します。',
    },
    add_action: 'エグゼキューターを追加',
    add_automation: '自動化タスクを追加',
    add_trigger: 'トリガーを追加',
    advanced_options: '高度なオプション',
    automation: '自動化タスク',
    cancel_delay: '遅延をキャンセル',
    cancel_delay_content:
      '遅延をキャンセルすると、遅延の後に続くステップは実行されません。実行をキャンセルしますか？',
    cancel_delay_success: '遅延をキャンセルしました',
    choose_imap_integration: 'IMAPメール統合',
    choose_integration: '既存の統合を選択',
    close_automation: '自動化を閉じる',
    closed: '閉じています',
    coming_soon_description: 'この機能は近日公開予定です！ 速度を上げるための提案をお願いします！',
    coming_soon_feedback: 'フィードバックありがとうございます！',
    coming_soon_placeholder: 'ここにフィードバックを残してください、ありがとうございます！',
    copy_of: '{name}のコピー',
    description_empty: '説明が設定されていません',
    description_help_text:
      '簡潔で明確な備考を記入してください。これは自動化の主画面に表示され、理解を助けます。',
    disabled: '閉じる',
    drop_files_here: 'Drop files here',
    duplicate_success: '複製成功',
    email_content_type_label: 'メールコンテンツタイプ',
    empty_history: '実行履歴はありません',
    empty_step: '自動化ステップが設定されていません',
    empty_step_log: 'このステップには履歴ログがありません',
    enable: '有効化',
    enabled: '開く',
    history: '実行履歴',
    history_status: {
      cancelled: 'キャンセル',
      delay: '遅延',
      failed: '失敗',
      running: '実行中',
      success: '成功',
      timeout: 'タイムアウト',
    },
    http_if_change: {
      every_failed: 'リクエスト失敗時',
      first_failed: '初回リクエスト失敗時',
      policy_title: 'ポリシー',
      response_changed: 'レスポンスが変更された時',
      response_compare_json_path: 'レスポンスのJSONパスを比較',
    },
    inbound_email: {
      description_guide: '検索条件をどのように書くか?',
      label_mailbox_archive: 'アーカイブ',
      label_mailbox_deleted: '削除済み',
      label_mailbox_drafts: '下書き',
      label_mailbox_inbox: '受信トレイ',
      label_mailbox_junk: '迷惑メール',
      label_mailbox_sent: '送信済み',
      placeholder_mailbox_name: 'フォルダ名を入力してください。デフォルトはINBOXです。',
      placeholder_search_criteria: 'メール検索条件を入力してください。入力しない場合',
      search_rule_description:
        '詳細情報については {help} の検索機能を参照してください。デフォルトでは、すべてのメールがメールボックスに含まれますが、記入しない場合もあります。',
      title_download_attachments: '添付ファイルをダウンロード',
      title_mailbox_name: 'フォルダ',
      title_search_criteria: 'カスタムメール検索条件',
      toast_imap_connection_error: 'IMAP接続に失敗しました。設定を確認してください',
    },
    input: '入力',
    item_output: 'アイテム出力',
    label: {
      manual_input: 'URLを手動で入力',
      select_integration: '統合からURLを抽出',
    },
    manual_execution_description: 'この自動化を今すぐ実行しますか?',
    manual_execution_success: '自動化タスクが開始されました',
    manual_trigger_fields: '動的フィールド',
    manual_trigger_fields_desc:
      'トリガーの前に入力する必要のあるフィールドを宣言します。これらのフィールドはワークフロー内のすべてのアクションで参照できます。',
    manual_trigger_result: 'テスト結果',
    manual_trigger_result_desc: '手動トリガーの結果',
    max_trigger_count: 'トリガーは最大3つまで設定できます',
    no_description: '説明が設定されていません',
    off: 'オフ',
    on: 'オン',
    open_automation: '自動化を開く',
    output: '出力',
    parameter_source: 'パラメータソース',
    parameter_source_description:
      'パラメータを手動で構成するか、選択した統合の設定を再利用するかを選択できます。',
    parameter_source_option1: '手動構成',
    parameter_source_option2: '統合設定を使用',
    placeholder_choose_integration: 'クリックして選択または追加',
    recent_history: '最近の実行履歴',
    recent_history_detail: '詳細',
    recent_history_id: 'ID',
    recent_history_start_time: '開始時刻',
    recent_history_status: '最近の実行履歴',
    record_list: 'レコードリスト',
    repeat_for_each_in: 'リスト内の各項目に対して以下のアクションを実行',
    report_content_description: 'レポートの内容を入力してください。Markdownをサポートします。',
    report_content_type_label: 'レポートコンテンツタイプ',
    report_markdown: 'レポートマークダウン',
    report_markdown_placeholder: 'レポートマークダウンを入力してください',
    report_prompt: 'レポートプロンプト',
    report_prompt_placeholder: 'レポートプロンプトを入力してください',
    report_subject: 'レポート件名',
    report_subject_placeholder: 'レポート件名を入力してください',
    report_type: 'レポートタイプ',
    round_robin: {
      label: {
        select_action: 'アクションを選択',
        select_database: 'データベースを選択',
        select_target: 'ターゲット',
        select_view: 'ビューを選択',
      },
      type: {
        database: 'データベース',
        database_view: 'データベースビュー',
        prev_action: '前の操作',
        round_type: 'ラウンドタイプ',
        user: 'ユーザー',
      },
    },
    run: {
      failed: '失敗',
      status: '実行{status}、時刻：{time}',
      success: '成功',
    },
    run_immediately: '今すぐ実行',
    run_immediately_description: 'この自動化はすぐに実行され、1回だけ実行されます',
    run_test: {
      description:
        'この自動化をテストして、トリガーとアクションが正しく動作するかどうかを確認します。',
      preview_button: 'プレビュー',
      run_button: 'テストを実行',
      title: 'テストを実行',
    },
    script: 'スクリプト',
    script_language: 'コード言語',
    send_message_to_slack: 'メッセージをSlackに送信',
    sort: '並べ替え',
    started: '始まっています',
    status: 'ステータス',
    then: 'その後',
    title_manual_execution: '自動化タスクを実行する',
    tooltip_learn_more: '詳細を学ぶ',
    tooltips: {
      step_error:
        '現在のステップで必要な構成項目が不足しているため、このステップは正常に動作しない可能性があります。ご確認ください',
    },
    trigger: {
      add_action: 'アクションを追加',
      add_trigger: 'トリガーを追加',
      button_clicked: {
        description: 'データベースボタンフィールドのボタンをクリックすると操作を実行します.',
        name: 'ボタンフィールドがクリックされたとき',
      },
      datetime_field_reached: {
        ahead: '前',
        days: '日数',
        delay: '遅れ',
        description: '指定された日付フィールドが指定された日付に達した時にトリガー。',
        hours: '時間',
        minutes: '分',
        name: '日付フィールドが到達した時',
        offset: 'オフセット',
        offset_day: '何日か前後にずらす',
        offset_day_placeholder: '負は前もって、正は遅延、0は当日',
        offset_hour: '何時間か前後にずらす',
        offset_hour_placeholder: '負は前もって、正は遅延、0は現在',
        offset_minute: '何分か前後にずらす',
        offset_minute_placeholder: '負は前もって、正は遅延、0は現在分',
        today: '今日',
      },
      description: '備考',
      dummy_trigger: {
        description: '自動化フローのトリガー条件をテストおよび検証するために使用されます。',
        name: 'ダミートリガー',
      },
      form_submitted: {
        description: '新しいフォーム提出があった時に自動的にトリガーされます。',
        name: '新規フォーム提出',
      },
      http_if_change: {
        description: 'HTTPレスポンスの内容に変更が検出された時に自動的にトリガーされます。',
        name: 'HTTPレスポンス変更',
      },
      inbound_email: {
        description: '特定のメールを受信した時に自動的にトリガーされます。',
        name: '特定メール受信',
      },
      manually: {
        description: 'ユーザーが手動でトリガーをクリックした時に操作を実行します。',
        name: '手動トリガー',
      },
      member_joined: {
        description: '新しいメンバーがスペースステーションに参加した時に操作を実行します。',
        name: '新規メンバー参加',
      },
      not_found: 'トリガーが見つかりません',
      record_created: {
        description: 'データベースに新しいレコードが追加された時に操作を実行します。',
        name: '新規レコード作成',
      },
      record_match: {
        description: '追加または編集されたレコードが条件を満たす時に操作を実行します。',
        name: 'レコード条件一致',
      },
      scheduler: {
        at_least_one_option: '少なくとも1つのオプションを選択してください',
        description: '設定された時間に達した時に自動的に操作を実行します。',
        name: 'スケジュールタスク',
      },
      select_database: 'データベースを選択',
      select_form: 'フォームを選択',
      select_form_have_not: '現在選択可能なフォームがありません',
      select_form_note: 'このトリガーは設定を完了するためにフォームを選択する必要があります',
      select_form_placeholder: 'フォームを選択してください',
      select_match: '一致条件',
      triggers: 'トリガー',
      type: 'トリガータイプ',
      webhook_received: {
        description:
          '外部システムがデータを Bika.ai に送信する必要がある場合に使用します。Webhook は、特定のイベントが発生した際に、第三者アプリケーション（ECサイト、CRMシステムなど）が自動的に Bika.ai に情報を送信できる固有のURLリンクを提供し、対応する自動化ワークフローをトリガーします。',
        name: 'Webhookトリガー',
        placeholder: '保存後に一意の Webhook リンクが生成されます',
        url: 'Webhookリンク',
      },
    },
    trigger_creation_success: 'トリガーを作成しました',
    trigger_help_urls: {
      form_submit: '/help/reference/node-resource/form',
      inbound_email: '/help/reference/automation-trigger/inbound-email',
    },
    trigger_history_empty: 'この実行は手動でトリガーされました',
    trigger_update_success: 'トリガーを更新しました',
    updating: '更新中',
    variable: {
      placeholder: '入力 "/" 挿入変数',
    },
    variable_select: {
      automation: {
        id: '自動化ID',
        interrupt_url: '中断自動化リクエストURL',
        name: '自動化名',
        run_history_id: '実行ID',
      },
      choose: '選択',
      createdAt: '作成日',
      data_empty: 'データがありません',
      database: {
        id: 'データベースID',
        name: 'データベース名',
        url: 'データベースURL',
      },
      databaseId: 'データベースID',
      database_title: 'データベース',
      field: {
        clear_all: 'すべてクリア',
        data: 'フィールドデータ',
        data_tips: 'フィールドの元データ。レコードの更新やスクリプト処理に使用します。',
        doc_id: 'ドキュメントID',
        doc_name: 'ドキュメント名',
        id: 'フィールドID',
        join: 'フィールド結合',
        name: 'フィールド名',
        select_all: 'すべて選択',
        type: 'フィールドタイプ',
        value: 'フィールド値',
        value_tips:
          'フォーマット済みのフィールドデータ。画面表示やメール・テキストへの挿入に適しています。',
      },
      fields: 'フィールド',
      getting_variable: '測定中です...',
      global: {
        key: 'グローバル',
        no_result_found: '検索履歴なし',
        official_website: '公式ウェブサイト',
      },
      id: 'ID',
      insert: '挿入',
      item: 'アイテム',
      item_actions: 'アイテムのアクション',
      item_description: 'アイテムの説明',
      item_title: 'アイテム',
      member_email: 'メンバーのメール',
      member_id: 'メンバーID',
      member_ids: 'メンバーIDリスト',
      member_ids_tips: 'メンバーIDで構成される配列で、ループ実行器で順番に処理できます',
      member_list: 'メンバーリスト',
      member_name: 'メンバー名',
      member_user_id: 'メンバーユーザーID',
      members: 'メンバー',
      members_length: 'メンバー数',
      members_tips: '複数のメンバーで構成される集合で、ループ実行器で順番に処理できます',
      menu_insert_variable_title: '変数を挿入',
      name: '名前',
      other_title: 'その他',
      placeholder: '変数を選択',
      primary_component: '主要コンポーネント',
      record: {
        cell: 'セル',
        grid_list: 'グリッドリスト(テキスト)',
        grid_tips: 'レコードをテキストテーブルとしてフォーマットします',
        id: 'レコードID',
        list_tips: 'レコードをテキストリストとしてフォーマットします',
        record_id_list: 'レコードIDリスト',
        record_id_list_tips: 'レコードIDで構成される配列で、ループ実行器で順番に処理できます',
        record_list: 'レコードリスト(テキスト)',
        record_tips: '現在操作中のレコードで、個別のフィールドを選択できます',
        records: 'レコード',
        records_length: 'レコード数',
        records_tips: '複数のレコードで構成される配列で、ループ実行器で順番に処理できます',
        selected_fields: '選択されたフィールド',
        url: 'レコードURL',
      },
      recordId: 'レコードID',
      record_title: 'レコード',
      resume_time: '再開時間',
      resume_time_tips: 'この時点でプロセスが再開されます',
      revision: 'リビジョン',
      round_robin_item: 'ラウンドロビンアイテム',
      run_time: '実行時間',
      run_time_tips: '自動化フローがトリガーされた時刻',
      select_data_placeholder: 'データを選択',
      select_data_title: 'データを選択',
      select_variable: '選択する',
      space: {
        home_page_url: 'スペースホームページURL',
        id: 'スペースID',
        name: 'スペース名',
        report_page_url: 'レポートページURL',
        todo_page_url: 'ToDoページURL',
      },
      updatedAt: '更新日',
      url: 'URL',
      variable_component: '変数コンポーネント',
    },
    webhook: {
      add_field: 'フィールドを追加',
      add_header: 'ヘッダーを追加',
      description_guide: '設定ガイド',
      description_message_content: 'Markdown形式をサポートし、"/"を入力すると変数を挿入できます',
      description_webhook_source:
        '現在のアクションには、メッセージ送信先としてWebhook URLを入力する必要があります。Webhook URLを手動で入力するか、既存の統合から選択して再利用することができます。',
      feishu_type_interactive: 'メッセージカード',
      feishu_type_post: 'リッチテキスト',
      feishu_type_text: 'テキストメッセージ',
      field_value: 'フィールド値',
      message_type_actioncard: 'ActionCard',
      message_type_link: 'リンク',
      message_type_templatecard: 'テンプレートカード',
      message_type_text: 'テキストメッセージ',
      placeholder_field_name: 'フィールド名を入力',
      placeholder_field_value: 'フィールド値を入力',
      placeholder_header_name: '名前',
      placeholder_header_value: '値',
      placeholder_request_method: 'リクエスト方法を選択',
      placeholder_request_url: 'リクエストURLを入力',
      placeholder_webhook_source: 'Webhookソースを選択してください',
      placeholder_webhook_url: 'Webhook URL を入力してください',
      support_format: 'サポートされている形式',
      title_body_type: 'ボディタイプ',
      title_content_type: 'コンテンツタイプ',
      title_form_data: 'フォームデータ',
      title_message_content: 'メッセージ内容',
      title_message_title: 'メッセージタイトル',
      title_message_type: 'メッセージタイプ',
      title_request_content: 'リクエスト内容',
      title_request_headers: 'ヘッダー',
      title_request_method: 'リクエスト方法',
      title_request_url: 'URL',
      title_webhook_source: 'Webhook ソース',
      title_webhook_url: 'Webhook URL',
      webhook_json_error: 'JSON形式のボディが無効です',
    },
    when: 'いつ',
  },
  avatar: {
    avatar: 'アバター',
    cancel_select: '選択をキャンセル',
    change_avatar: 'アバターを変更',
    edit_image: '編集',
    file_tip: 'JPG、PNG、GIF形式に対応、画像サイズは2MB以内',
    gif_no_crop_tip:
      'GIFファイルはアニメーションを保持するため、トリミングせずにアップロードされます',
    paste_image_link: '画像リンクを貼り付けてください...',
    preview: 'プレビュー',
    preview_avatar: 'アバタープレビュー',
    re_select: '再選択',
    select_from_gallery: 'ギャラリーから選択',
    tab_color: '色',
    tab_link: 'リンク',
    tab_link_tip: 'インターネット上の任意の画像に適用されます。',
    tab_upload: 'アップロード',
    take_photo: '写真を撮る',
    upload_avatar: 'アバターをアップロード',
  },
  brand: {
    about_brand: 'Bika.aiについて',
    brand: 'Bika.ai',
    website: '公式サイト',
  },
  buttons: {
    add: '追加',
    add_virtual_intelligent_task: '仮想インテリジェントタスクを追加',
    back: '戻る',
    back_to_space: '私のスペースに戻る',
    cancel: 'キャンセル',
    change_bound_email: 'バインドメールアドレスを変更',
    close: '閉じる',
    completed: '完了',
    confirm: '確認',
    create: '作成',
    create_space: 'スペースを作成',
    delete: '削除',
    edit: '編集',
    more: 'もっと見る...',
    pre_fill_title_btn: '予備充填',
    remove: '削除',
    run: '実行',
    save: '保存',
    see_more: 'もっと見る',
    send_your_suggestion: '提案を送信',
    submit: '送信',
    view_all: 'すべて表示',
  },
  cancel: 'キャンセル',
  coming_soon: '近日公開',
  components: {
    breadscrumb: {
      root: 'ルート',
    },
    configure_multilingual: '多言語を設定',
    confirm_remove_multilingual_configuration: '多言語設定を削除してもよろしいですか？',
    disable_multilingual_warning: '無効にすると、既存の内容がクリアされます',
    enable_multilingual_warning: '有効にすると内容がクリアされ、言語が再設定されます',
    remove_multilingual_configuration_warning:
      '削除すると復元できません。一つの言語のみが保持されます。追加する場合は再設定が必要です。',
    view_all_languages: 'すべての言語を表示',
  },
  confirm: '確認',
  copy: {
    copy: 'コピー',
    copy_link: 'リンクをコピー',
    copy_link_to_clipboard: 'リンクをクリップボードにコピー',
    copy_success: 'コピー成功',
    create_short_url: '短いリンクを作成',
    delete_short_url: '短縮URLを削除',
  },
  dashboard: {
    add_widget: 'ウィジェットを追加',
    select_data_source: 'データソースを選択',
    widget_not_editable: 'このウィジェットは変更できません。',
  },
  dashboard_widgets: {
    ai_widget: {
      description: 'A Widget that AI-generated content',
      name: 'AI Widget',
    },
    bika: {
      description: 'bikaを表示するコンポーネント',
      name: 'bika',
    },
    chart: {
      description:
        '表内のデータを棒グラフ、折れ線グラフ、散布図、円グラフなど、さまざまな形式で視覚化する',
      name: 'チャート',
    },
    embed: {
      description: 'URLを入力すると、他のウェブサイトのコンテンツを埋め込むことができる',
      name: '埋め込み',
    },
    icons: {
      description: 'アイコンを表示するコンポーネント',
      name: 'アイコン',
    },
    list: {
      description: 'リストを表示するコンポーネント',
      name: 'リスト',
    },
    number: {
      description:
        '表内の任意の列のデータを集計し、その集計値を小さなアプリケーション上で目立つスタイルで表示する',
      name: 'ハイライトナンバー',
    },
    pivot_table: {
      description: '詳細データテーブルを迅速に分類集計するデータ分析ツール',
      name: 'ピボットテーブル',
    },
    progress_bar: {
      description: '進行状況を表示するコンポーネント',
      name: 'プログレスバー',
    },
    text: {
      description: 'テキストを表示するコンポーネント',
      name: 'テキストブロック',
    },
  },
  data: {
    data: 'データ',
    database: 'データベース',
  },
  database_fields: {
    ai_photo: {
      description:
        'AIによって自動生成された画像やイメージコンテンツで、製品展示、ソーシャルメディアなどのシーンに適しています',
      name: 'AI画像',
    },
    ai_text: {
      ai_write: 'AI による生成',
      ai_writing: 'AIが書いています...',
      auto_update: '自動更新',
      description:
        'AIが自動的にテキストコンテンツを生成し、表内のデータを参照できます。カスタマーサポートの返信、製品説明、コンテンツの要約などのシーンに適しており、創作効率を向上させます',
      llm_provider: 'AIサービスプロバイダー',
      name: 'AIテキスト',
      preview_btn: 'プレビュー',
      preview_empty: 'プレビュー結果がありません',
      preview_result: 'プレビュー結果',
      preview_result_description: 'このプレビューはこの表の最初のレコードに基づいています',
      prompt_description:
        'AIにメッセージコンテンツをどのように生成すべきかを伝えます。「/」を入力して表のフィールドを挿入できます',
      prompt_menu_title: 'フィールドを選択',
      prompt_title: 'AIテキスト',
    },
    ai_video: {
      description:
        'AIによって自動生成された動画やアニメーションコンテンツで、広告宣伝、ソーシャルメディアなどのシーンに適しています',
      name: 'AI動画',
    },
    ai_voice: {
      description:
        'AIによって自動生成された音声やオーディオコンテンツで、音声アシスタント、ポッドキャストなどのシーンに適しています',
      name: 'AI音声',
    },
    api: {
      description: 'API情報を保存し、外部システムとの連携やデータ交換に使用します',
      name: 'API',
    },
    attachment: {
      adaptive: '適応',
      close: '閉じる',
      copy_link: 'リンクをコピー',
      copy_link_success: 'リンクをコピーしました',
      delete: '削除',
      description:
        '文書、画像、圧縮ファイルなど、様々なタイプのファイルをレコードの添付ファイルとしてアップロードし保存できます',
      download: 'ダウンロード',
      download_success: 'ダウンロードしました',
      initial_size: '初期サイズ',
      name: '添付ファイル',
      rotate: '回転',
      zoom_in: 'ズームイン',
      zoom_out: 'ズームアウト',
    },
    auto_number: {
      description:
        '新しいレコードごとに自動的にユニークな連番を生成します。注文番号、チケット番号などのシーンに適しています',
      name: '自動採番',
    },
    base: {
      field_description: 'フィールドの説明',
      field_name: 'フィールド名',
      field_type: 'フィールドタイプ',
      field_type_placeholder: '列タイプを選択してください',
    },
    button: {
      description:
        'クリック可能なインタラクティブボタンを作成し、クリック後に設定済みの自動化操作やイベントをトリガーできます',
      name: 'ボタン',
    },
    cascader: {
      description:
        '多階層の連動するドロップダウンメニューを提供し、階層関係のあるデータ選択に適しています。例えば地域選択など',
      name: '階層選択',
    },
    checkbox: {
      description:
        'はい/いいえのオプションを提供するチェックボックスで、状態のマークやシンプルなブール値の選択に適しています',
      name: 'チェックボックス',
    },
    created_by: {
      description:
        'そのレコードを作成したユーザー情報を自動的に記録し、レコードの出所を追跡しやすくします',
      name: '作成者',
    },
    created_time: {
      description: 'そのレコードが作成された具体的な日付と時間を自動的に記録します',
      name: '作成日時',
      property: {
        auto_fill: '自動入力',
        date_format: '日付形式',
        date_format_placeholder: '日付形式を選択してください',
        show_time: '時間を表示',
        time_format: '時間形式',
      },
    },
    currency: {
      description:
        '通貨金額の保存とフォーマットに特化しており、異なる通貨記号や精度設定をサポートします',
      name: '通貨',
      property: {
        accuracy: '精度',
        alignmen_default: 'デフォルト配置',
        alignmen_left: '左揃え',
        alignmen_right: '右揃え',
        alignment: '記号の配置方法',
        symbol: '記号',
        symbol_placeholder: '通貨記号を入力してください',
      },
    },
    cut_video: {
      description: '編集または切り取られた動画クリップを保存し、編集情報とタイムラインを保持します',
      name: '動画編集',
    },
    daterange: {
      description:
        '期間や日付範囲を保存し、開始と終了の2つの時間点を含みます。プロジェクト期間、イベント時間などに適しています',
      name: '日付範囲',
      property: {
        date_format: '日付形式',
        date_format_placeholder: '日付形式を選択してください',
        show_time: '時間を表示',
        time_format: '時間形式',
      },
    },
    datetime: {
      description:
        '正確な日付と時間情報を保存します。特定の時間点を記録する必要があるシーンに適しています',
      name: '日時',
      property: {
        auto_fill: '新規レコード作成時に自動的に作成時間を入力',
        date_format: '日付形式',
        date_format_placeholder: '日付形式を選択してください',
        show_time: '時間を表示',
        time_format: '時間形式',
      },
      repeat_day: '日ごと',
      repeat_hour: '時間ごと',
      repeat_minute: '分ごと',
      repeat_month: '月ごと',
      repeat_week: '週ごと',
      repeat_year: '年ごと',
    },
    email: {
      description: 'メールアドレスの保存に特化しており、連絡先情報、通知などのシーンに適しています',
      name: 'メール',
    },
    formula: {
      description:
        '数式を通じて値を自動計算し、他のフィールドを参照して数学的または論理的な演算を行うことができます',
      name: '数式',
      property: {
        expression: '数式',
        expression_placeholder: '数式を入力してください',
      },
    },
    json: {
      description:
        '構造化されたJSON形式のデータを保存し、複雑なデータ構造やAPIレスポンスの内容に適しています',
      name: 'JSON',
    },
    link: {
      description: '他の表との双方向関連付けを作成し、表間のデータの相互参照と関係維持を実現します',
      name: '関連付け',
      property: {
        relation_database: '関連表',
      },
    },
    long_text: {
      description: '詳細な説明、コメント、記事本文など、長文テキスト内容を保存するために使用します',
      name: '複数行テキスト',
    },
    lookup: {
      description:
        '関連表から特定のフィールドの値を自動的に検索して表示し、データの動的な参照を実現します',
      name: '参照検索',
      property: {
        error: '対応するデータベースが見つかりません',
        lookup_field: '検索フィールド',
        select_link_database: '関連表を選択',
      },
    },
    member: {
      description:
        'システムメンバー情報を保存し、単一または複数のメンバーをフィールド値として選択できます',
      name: 'メンバー',
      property: {
        allow_multiple: '複数のメンバーの追加を許可',
        notify_mentioned: 'メンバー選択後にメッセージ通知を送信',
      },
    },
    modified_by: {
      description: 'そのレコードを最後に変更したユーザー情報を自動的に記録します',
      name: '更新者',
    },
    modified_time: {
      description: 'そのレコードが最後に変更された日付と時間を自動的に記録します',
      name: '更新日時',
      property: {
        auto_fill: '自動入力',
        date_format: '日付形式',
        date_format_placeholder: '日付形式を選択してください',
        show_time: '時間を表示',
        time_format: '時間形式',
      },
    },
    multi_select: {
      description:
        '定義済みのオプションリストから複数のオプションを選択できます。複数タグ分類に適しています',
      name: '複数選択',
      property: {
        add_options: 'オプションを追加',
        default_value: 'デフォルト値',
      },
    },
    number: {
      description: '整数や小数などの数値データを保存し、精度やフォーマットの設定が可能です',
      name: '数値',
      property: {
        custom_units: 'カスタム単位',
        custom_units_default: '単位名を入力してください',
        precision: '精度',
        thousand_separator: '桁区切り',
      },
    },
    one_way_link: {
      description:
        '他の表との一方向の関連付けを作成し、現在の表からのみ関連表のデータを閲覧できます',
      name: '一方向リンク',
    },
    percent: {
      description:
        'パーセント値を保存し、自動的にパーセント形式で表示します。進捗状況、比率などのシーンに適しています',
      name: 'パーセント',
      property: {
        default_value: 'デフォルト値',
        precision: '精度',
      },
    },
    phone: {
      description: '電話番号の保存に特化しており、連絡先情報、顧客データなどのシーンに適しています',
      name: '電話番号',
    },
    photo: {
      description: '画像ファイルの保存と表示をサポートし、プレビューとサムネイル機能を提供します',
      name: '写真',
    },
    rating: {
      description: '星の数や数値形式で評価情報を保存し、評価レベルを視覚的に表示します',
      name: '評価',
      property: {
        icon_settings: 'アイコン設定',
        max_value: '最大値',
      },
    },
    single_select: {
      description:
        '定義済みのオプションリストから単一のオプションを選択できます。状態や分類のシーンに適しています',
      name: '単一選択',
      property: {
        add_options: 'オプションを追加',
        default_value: 'デフォルト値',
      },
    },
    single_text: {
      description: '短い単一行のテキストを保存します。タイトル、名前などの簡潔な情報に適しています',
      name: '単一行テキスト',
    },
    url: {
      description:
        'ウェブページのリンクアドレスを保存し、直接リンクをクリックしてアクセスする機能をサポートします',
      name: 'URL',
    },
    video: {
      description: '動画ファイルを保存し、アップロード、プレビュー、再生機能をサポートします',
      name: '動画',
    },
    voice: {
      description: '音声ファイルを保存し、録音、アップロード、再生機能をサポートします',
      name: '音声',
    },
    work_doc: {
      description:
        'Markdown形式をサポートするリッチテキスト文書を保存し、セル内で直接文書内容を作成・編集できます',
      name: 'ドキュメント',
    },
  },
  database_views: {
    form: {
      description:
        'フォームビューを使用すると、ユーザーはカスタムフォームを作成して、データ入力と収集を簡素化できます。ユーザーはフォームリンクを共有して外部ユーザーからデータを収集し、収集したデータをシステムに自動的に追加できます。データ入力と収集プロセスを簡素化する必要があるシナリオに適しています',
      name: 'フォーム',
    },
    gallery: {
      description:
        'アルバムビューは、レコードをカード形式で表示し、レコードの添付ファイルの画像をカバーとして使用します。名刺、資料、メニューなどのシーンに適しています',
      name: 'アルバム',
    },
    gantt: {
      description:
        'ガントビューは、タイムライン上にプロジェクトの進捗状況を表示し、タスクの開始と終了時間、およびタスク間の依存関係を視覚的に確認できます。プロジェクトのタイムラインを効果的に計画および管理する必要があるシナリオに最適です',
      name: 'ガント',
    },
    kanban: {
      description:
        'かんばんビューは、カード形式でデータを表示し、各列はステータスまたはカテゴリを表します。ユーザーはカードを列間でドラッグ＆ドロップして、タスクやプロジェクトの進捗を反映させることができます。ワークフローやタスクの進捗を視覚的に追跡する必要があるシナリオに最適です',
      name: 'カンバン',
    },
    table: {
      description:
        'テーブルビューは、スプレッドシートのようなレイアウトを提供し、ユーザーはデータを構造化された方法で表示および管理できます。各列はフィールドを表し、各行はレコードを表し、データの迅速なブラウズ、フィルタリング、並べ替えが可能です。大量のデータを明確かつ整理して管理する必要があるシナリオに適しています',
      name: 'テーブル',
    },
  },
  delete: {
    confirm_deletion: '削除を確認',
    confirm_to_delete_content: 'このコンテンツを削除しますか？',
    confirm_to_delete_this_link: 'このリンクを削除しますか?',
    confirm_to_delete_title: '削除するか確認する',
    delete: '削除',
    delete_success: '削除成功',
  },
  document: {
    code_placeholder: 'コードを入力してください...',
    list_placeholder: 'リストアイテムを入力してください...',
    status_connected: '接続済み',
    status_connecting: '接続中',
    status_disconnected: '未接続',
    text_placeholder: '入力 "/" 挿入',
    title_placeholder: 'タイトルを入力してください',
  },
  editor: {
    add_button_text: '追加',
    add_column_as_sort_condition:
      '参照フィールドまたは参照フィールドがあるテーブルの任意のフィールドをソート条件として追加できます',
    add_condition: '条件を追加',
    add_filter_condition: 'フィルター条件を設定',
    add_skill: '追加',
    add_sort_condition: 'ソート条件を設定',
    aggegate_records: '参照データを集計する',
    all_count: '全部計数',
    all_record: '全部',
    and_cal: 'AND 演算',
    and_condition: 'そして"{field}"が"{condition}""{value}"のとき',
    approval: '承認',
    average: '平均値',
    button_refresh: '更新',
    by_filter_and_sort: '参照フィールドをフィルターして並べ替える',
    card_style: 'カードスタイル',
    collapse: '折りたたみ',
    collapse_all_group: 'すべてのグループを折りたたむ',
    collapse_group: 'グループを折りたたむ',
    column_required: '{name}は必須項目です',
    concat_as_text: 'テキストとして連結',
    concat_by_semicon: 'セミコロンで連結',
    condition: '"{field}"が"{condition}""{value}"のとき',
    content_paste_failure: '貼り付け失败',
    content_paste_successfully: '貼り付け成功',
    count_sort_and_filter_confition:
      '{filterConditionCount}個のフィルター条件と{sortConditionCount}個のソート条件',
    create_mission_line_text: 'ミッションを作成',
    create_record_line_text: '新規作成',
    current_data_not_allowed: 'このセルにデータを書き込むことができません',
    custom_skill: 'カスタムスキル',
    data_import_process: '{Percent} データをアップロード中、データを一時的に更新できません',
    delete_n_records_in_list: 'リストから選択した{count}件のレコードを削除',
    delete_record_warning_content: '削除してもよろしいですか？',
    enable: '有効化',
    excel_import_success: '{Count}件のデータを正常にインポートしました',
    expand_all_group: 'すべてのグループを展開',
    expand_group_or_sub_group: 'グループ/サブグループを展開',
    filter_no_nil: 'すべての空の値をフィルタリング',
    find_records_line_text: '検索',
    first_record: '最初の記録',
    grid_row_height: '行の高さ',
    grid_row_height_default: 'デフォルト',
    grid_row_height_extra_large: '特高',
    grid_row_height_large: '高',
    grid_row_height_medium: '中程度',
    grid_row_height_seeting: '高さ設定',
    integration_line_text: '構成の統合',
    item_not_supported_currently: 'このオプションは現在サポートされていません',
    lookup_count: '参照数',
    lookup_original_values: '関連テーブルからそのままデータを引用する',
    maximal_value: '最大値',
    microphone_disabled:
      'マイクの使用許可が拒否されました。ブラウザの権限設定を更新して、再度お試しください。',
    minimal_value: '最小値',
    no_search_result: '一致する検索結果がありません',
    not_nil_count: '非空値カウント',
    not_null_count: '非空値計数',
    number_value_format: '数値形式',
    operate_successfully: '操作成功',
    or_calulate: 'または演算',
    or_condition: 'または"{field}"が"{condition}""{value}"のとき',
    original_values: '原本の値',
    please_add_link_field: '関連フィールドを追加してください (LINK, ONEWAY_LINK)',
    please_input_data: 'データを入力してください',
    please_select_a_skillset: 'スキルセットを選択してください',
    please_select_at_least_one_skill: '少なくとも1つのスキルを選択してください',
    please_select_configuration: '構成を選択してください',
    record_filter_out_tips: 'フィルタリングされたレコードは参照されません',
    remove_dulplicated: '重複を削除',
    select_skill: 'AI エージェントにスキルを追加してください',
    show_cover: '展示封面',
    show_field_name: 'フィールド名を表示',
    show_field_name_help_text:
      'カードにフィールド名を表示します。無効にすると、フィールド値のみが表示されます。',
    show_logo: 'ロゴを表示',
    show_time_zone_info: 'タイムゾーン情報を表示',
    skillsets: 'スキルセット',
    start_paste: '貼り付け中...',
    stop_microphone: '停止',
    sum_value: '合計',
    sum_value_tooltip: 'すべての値の合計を返しますn SUM(1, 3, 5, "", "Apple") => 9 (1+3+5)',
    symbol_align_strategy: 'シンボル揃えの戦略',
    table_lock_message: 'を更新できません。後でもう一度お試しください',
    text_area_tips: 'Enter で改行、Shift + Enter で編集終了',
    this_field_configuration_missing:
      'このフィールドに構成エラーがあります。式またはフィールドで使用される構成を確認してください。',
    this_field_not_allow_edit: 'このフィールドは編集されません',
    upload_image: '画像をアップロード',
    upload_image_banner_size: '推奨サイズ: 1440*480',
    use_micro_phone: 'マイクを使用',
    xor_calculate: '排他的論理和',
    you_have_no_saved_change: '未保存の変更',
    you_have_no_saved_change_content:
      'このウィンドウを閉じてもよろしいですか？変更内容は保存されません。',
  },
  email: {
    bcc: 'BCC',
    body: '本文',
    cc: 'CC',
    from_email: '送信者メール',
    from_name: '送信者名',
    help_text_reply_to: 'このメールに返信するためのメールアドレス。',
    provider: {
      service: 'Bika メールサービス',
      smtp: 'カスタムSMTP',
      smtp_integration: 'SMTP 統合',
    },
    provider_type: 'メールプロバイダー',
    recipient: '受信者',
    reply_to: '返信先',
    send_email: 'メールを送信',
    smtp_host: 'SMTP ホスト',
    smtp_password: 'SMTP パスワード',
    smtp_port: 'SMTP ポート',
    smtp_username: 'SMTP ユーザー名',
    subject: '件名',
  },
  error: {
    back: '戻る',
    back_to_home: 'ホームに戻る',
    error: 'エラー',
    error_code: 'エラーコード',
    error_description: 'エラー説明',
    error_message: 'エラーメッセージ',
    export: {
      record_limit:
        '現在のデータテーブルのレコードが50,000行を超えているため、エクスポートできません',
    },
    node_server_error: '現在のノードが削除されているか、存在しません',
    oops: 'おっと！',
    page_error: 'ページでいくつかのエラーが発生しました',
    page_not_found: 'ページが見つかりません',
    page_not_found_description:
      'お探しのページが見つかりません。前のページに戻るか、ホームに戻ることができます。',
    screen_not_found: '画面が見つかりません',
    space: {
      back: 'マイスペースステーションに戻る',
      description:
        'リンクが正しいことを確認し、アクセス権があることを確認してください。疑問がある場合は、タスクの発行者に連絡してください',
      title: 'このリンクの内容を表示する権限がありません',
    },
  },
  explore: {
    explore: '探検する',
  },
  filter: {
    and: 'かつ',
    contains: 'を含む。。。',
    date_after_or_equal: 'またはその次...',
    date_before_or_equal: 'またはその前に...',
    date_range: '日付範囲',
    does_not_contains: '含まれていません。。。',
    equal: 'はい',
    exact_date: '指定日',
    function_date_time_after: '…の後に。。。',
    function_date_time_before: 'の前に。。。',
    is_empty: '空白',
    is_not_empty: '空ではありません',
    is_repeat: '重複あり',
    name: 'フィルター',
    not_equal: 'いいえ。。。',
    or: 'または',
    previous_month: '先月',
    previous_week: '先週',
    search: '検索',
    settings_descritpion: 'ビューへの変更はまだ保存されていません',
    settings_name: 'フィルタリング設定',
    some_day_after: '何日後',
    some_day_before: '何日前',
    start_end_date: '開始時間 - 終了時間',
    the_last_month: '過去 30 日間',
    the_last_week: '過去 7 日間',
    the_next_month: '未来 30 日間',
    the_next_week: '未来 7 日間',
    this_month: '今月',
    this_week: '今週',
    this_year: '今年',
    today: '今日',
    tomorrow: '明日',
    where: '条件',
    yesterday: '昨日',
  },
  formula: {
    abs: {
      description:
        '説明\n数値の絶対値を返します。\n\nパラメータの説明\nvalue：絶対値を求める数値です。\n絶対値：正の数の絶対値はそのまま、負の数の絶対値は負号を取り除いた数です。',
      example:
        '// value > 0\n式：ABS(1.5)\n結果：1.50\n\n//value = 0\n式：ABS(0)\n結果：0.00\n\n// value < 0\n式：ABS(-1.5)\n結果：1.50',
      name: 'Abs',
    },
    and: {
      description:
        'すべてのパラメータが真（true）である場合、真（true）を返し、それ以外の場合は偽（false）を返します。\n\n【logical】は論理パラメータで、論理値、配列、またはフィールドの参照である可能性があります。',
      example: 'AND(3>2, 4>3)\n=> true',
      name: 'And',
    },
    array: '配列',
    array_compact: {
      description:
        '配列から空の文字列とnull値を削除します。\n\n【item】配列の値を表します。例えば、マルチセレクト、添付ファイル、マジックリンク、マジックリファレンスフィールドタイプのセル値など。\n\nこの関数は「false」値と空白文字の文字列を保持します。',
      example: 'ARRAYCOMPACT([1,2,"",3,false," ", null])\n=> [1,2,3,false," "]',
      name: 'Array Compact',
    },
    array_flatten: {
      description:
        '配列のネストを削除して平坦化します。すべてのデータが同じ配列の要素になります。\n\n【item】配列の値を表します。例えば、マルチセレクト、添付ファイル、マジックリンク、マジックリファレンスフィールドタイプのセル値など。',
      example: 'ARRAYFLATTEN([1, 2, " ", 3, ],[false])\n=> [1, 2, 3 ,false]',
      name: 'Array Flatten',
    },
    array_join: {
      description:
        '配列内のすべての値を区切り文字で連結して1つの文字列にします。\n\n【item】配列の値を表します。例えば、マルチセレクト、添付ファイル、マジックリンク、マジックリファレンスフィールドタイプのセル値など。',
      example: 'ARRAYJOIN({趣味} , "; ")',
      name: 'Array Join',
    },
    array_unique: {
      description:
        '配列内の一意の項目のみを返します。\n\n【item】配列の値を表します。例えば、マルチセレクト、添付ファイル、マジックリンク、マジックリファレンスフィールドタイプのセル値など。',
      example: 'ARRAYUNIQUE([1,2,3,3,1])\n=> "[1,2,3]',
      name: 'Array Unique',
    },
    average: {
      description:
        '複数の数値の算術平均を返します。\n\n【number...】は計算する数値パラメータで、数値や数値型の列を参照できます。数値型の列には、数値、通貨、パーセンテージ、評価などが含まれます。\n\nパラメータの中にテキスト値が含まれている場合、例えば""八""、計算時には0として扱われます。',
      example: 'AVERAGE(2, 4, "6", "八") => (2 + 4 + 6) / 4 = 3',
      name: 'Average',
    },
    blank: {
      description:
        '空の値を返します。\n\nセルが空であるかどうかを判断するために使用できます（例1参照）。\nセルに空の値を入力するために使用できます（例2参照）。',
      example:
        'IF({開始時間} = BLANK(), "未定", "確定")\n\nIF({数学の成績} ≥ 60, BLANK(), "再試験が必要")',
      name: 'Blank',
    },
    ceiling: {
      description:
        '数値を指定された基数の最も近い倍数に切り上げます。\n\n【value】は切り上げる値です。\n【significance】は任意で、切り上げに使用する基数です。返される値は基数の倍数です。指定しない場合、デフォルトは1です。\n【切り上げ】とは、元の数値以上で最も近い基数の倍数に値を返すことを意味します。',
      example: 'CEILING(1.99)\n=> 2\n\nCEILING(-1.99, 0.1)\n=> -1.9',
      name: 'Ceiling',
    },
    concatenate: {
      description:
        '複数のテキスト値を連結して1つのテキスト値にします。（&と同じ効果）\n\n【text1..】は連結する複数の値です。テキスト、数値、日付パラメータ、または列データの参照を入力できます。\n\n連結するテキスト値はダブルクォートで囲んでください。数値と参照列は除きます。\n特例：ダブルクォートを連結する場合は、バックスラッシュ（\\）をエスケープ文字として使用する必要があります。',
      example: 'CONCATENATE({名前}, {年齢}, "歳")\n\nCONCATENATE("\\"", {年齢}, "\\"")',
      name: 'Concatenate',
    },
    count: {
      description:
        '「数値」タイプの値の数をカウントします。\n\n【number】入力パラメータまたは参照列で指定できます。\n\nこの関数は、入力パラメータまたはセル内に含まれる数値（数値、通貨、パーセンテージ、評価など）をカウントできます。',
      example: 'COUNT(1, 3, 5, "", "七")\n=> 3',
      name: 'Count',
    },
    count_a: {
      description:
        '空でない値の数をカウントします。\n\n【textOrNumber】入力パラメータまたは参照列で指定できます。\n\nこの関数は、入力パラメータまたはセル内に含まれる空でない値の数をカウントできます。\n例えば、セル内にいくつのオプション、画像、メンバーがあるかをカウントできます。\nまた、マジックリファレンスセル内の配列の空でない値をカウントすることもできます。',
      example: 'COUNTA(1, 3, 5, "", "七")\n=> 4',
      name: 'CountA',
    },
    count_all: {
      description:
        '空の値を含むすべての値の数をカウントします。\n\n【textOrNumber】入力パラメータまたは参照列で指定できます。\n\nこの関数は、入力パラメータおよびセル内に含まれるすべての値（空の値を含む）をカウントできます。',
      example: 'COUNTALL(1, 3, 5, "", "七")\n=> 5',
      name: 'CountAll',
    },
    count_if: {
      description:
        'values内でkeywordが出現する回数をカウントします。\n\nvalues：データを検索する場所を指定します。配列タイプまたはテキストタイプのデータをサポートします。\nkeyword：検索してカウントするキーワード。\noperation：比較演算子、省略可能。条件記号「>」、「<」、「=」、「!=」を入力できます。入力しない場合、デフォルトは「=」です。\n例1では比較演算子を入力していないため、「A」と等しい値の出現回数をカウントします。\n例2では比較演算子「>」を入力しているため、「2」より大きい値の出現回数をカウントします。\n\n使用シナリオ：\n1）テキスト配列[A, B , C , D, A]内で文字「A」が出現する回数をカウントできます（例1）。\n2）数値配列[1, 2, 3, 4, 5]内で「3」より大きい数値の数をカウントできます（例2）。\n3）テキスト文字列「葡萄を食べて皮を吐かない」内で「葡萄」が出現する回数をカウントできます（例3）。',
      example:
        'COUNTIF({評価}, "A")\n=> 2\n\nCOUNTIF({スコア}, 3, ">")\n=> 2\n\nCOUNTIF({早口言葉}, "葡萄")\n=> 2\n',
      name: 'CountIf',
    },
    created_time: {
      description: 'レコードの作成時間を返します。',
      example: 'CREATED_TIME()\n=> "2024-06-10"\n\n"作成日：" & CREATED_TIME()',
      name: 'Created Time',
    },
    date: '日付',
    date_add: {
      description:
        '概要\n指定された日付に一定の時間間隔を追加します。\n\nパラメータの説明\ndate：指定された日付です。この関数は、その日付に基づいて一定の時間間隔を追加します。\ncount：時間間隔で、正負の数値を入力できます。正の数値の場合、指定された日付に数日（カスタム時間単位）を追加します（例1参照）。負の数値の場合、指定された日付から数日を減少させます（例2参照）。\nunits：時間単位で、時間間隔を追加する単位です。たとえば、「日」で計算することも、「年」に変換することもできます。\n\n時間単位には以下の記号が含まれ、2つの形式のいずれかを使用できます：「単位説明符」→「略語」\nミリ秒：「milliseconds」 → 「ms」\n秒：「seconds」 → 「s」\n分：「minutes」 → 「m」\n時間：“hours” → “h”\n日：“days” → “d”\n週：“weeks” → “w”\n月：“months” → “M”\n四半期：“quarters” → “Q”\n年：“years” → “y”\n\n以下のリンクをクリックして、すべての時間単位を確認できます。',
      example:
        '// 2024/03/25に1日の時間間隔を追加します。時間単位 "days" は「日」で計算します。\n式：DATEADD("2024/03/25", 1, "days")\n計算結果：2024/03/26\n\n// 2024/03/25から1日の時間間隔を減少させます。時間単位 "days" は「日」で計算します。\n式：DATEADD("2024/03/25", -1, "days")\n計算結果：2024/03/24\n\n// {開始時間}に10日の時間間隔を追加します。以下のフィールド {開始時間} は日付タイプで、セルの値は2024/03/25です。時間単位 "days" は「日」で計算します。\n式：DATEADD({開始時間}, 10, "days")\n計算結果：2024/04/04',
      name: 'Date Add',
    },
    datestr: {
      description:
        '日付を「年-月-日」形式のテキストにフォーマットします（固定フォーマットはYYYY-MM-DD）\n\n【date】フォーマットする日付\n\n日付がフォーマットされると、テキストの文字列になり、日付データの属性を持たなくなります。',
      example: 'DATESTR("2024/10/01")\n=> 2024-10-01\n\nDATESTR({開始時間})\n=> 2024-06-10',
      name: 'Datestr',
    },
    datetime_diff: {
      description:
        '2つの日付の差を返します（正負あり）。つまり、日付1から日付2を引きます。\n\n【date1】日付1\n【date2】日付2\n【units】時間単位で、日付1と日付2の差を計算する単位です。たとえば、「日」で計算することも、「年」に変換することもできます。\n\n時間単位には以下の記号が含まれ、2つの形式のいずれかを使用できます：「単位説明符」→「略語」\nミリ秒："milliseconds" → "ms"\n秒："seconds" → "s"\n分："minutes" → "m"\n時間："hours" → "h"\n日："days" → "d"\n週："weeks" → "w"\n月："months" → "M"\n四半期："quarters" → "Q"\n年："years" → "y"\n\n以下のリンクをクリックして、すべての時間単位を確認できます。',
      example:
        'DATETIME_DIFF( "2024-08-11"  ,"2024-08-10", "days")\n=> 1\n\n\nDATETIME_DIFF( "2024-08-9" ,"2024-08-10", "days")\n=> -1\n\nDATETIME_DIFF( {締め切り時間} , TODAY(), "hours")\n=> 48',
      name: 'Datetime Diff',
    },
    datetime_format: {
      description:
        '日付をカスタム形式でテキストにフォーマットします。\n\n【date】フォーマットする日付。\n【output_specifier】選択したフォーマット指定子。例えば、指定子は以下のようになります：\n"DD-MM-YYYY"は"日-月-年"を表します（例1参照）；\n" YYYY / MM / DD"は"年/月/日"を表します（例2参照）；\n"MM.DD"は"月.日"を表します（例3参照）。\n\n日付がフォーマットされると、テキストの文字列になります。\n\nこの関数がサポートする日付フォーマット指定子については、以下のリンクを参照してください。',
      example:
        'DATETIME_FORMAT("2024-10-01", "DD-MM-YYYY")\n=> 01-10-2024\n\nDATETIME_FORMAT("2024-10-01", "YYYY / MM / DD")\n=>2024/10/01\n\nDATETIME_FORMAT("2024-10-01", "MM.DD")\n=>10.01\n\nDATETIME_FORMAT(TODAY(), "DD-MM-YYYY")\n=> 01-10-2024',
      name: 'Datetime Format',
    },
    datetime_parse: {
      description:
        'テキストを構造化された日付型に変換します。\n\n【date】フォーマットするテキストの日付。\n【input_format】任意、このパラメータは日付フォーマット指定子です。システムが認識できないテキスト日付の内容については、自分で構造化された日付として解釈できます（例2参照）。\n\nこの関数がサポートする日付フォーマット指定子とロケールについては、以下のリンクを参照してください。',
      example:
        'DATETIME_PARSE("20241001")\n=> "2024/10/01"\n\nDATETIME_PARSE("01 10 2024 18:00", "DD MM YYYY HH:mm")\n=> "2024/10/01 18:00"\n\nDATETIME_PARSE("01号10月2024年18:00時", "DD号MM月YYYY年HH:mm時")\n=> "2024/10/01 18:00',
      name: 'Datetime Parse',
    },
    day: {
      description:
        '指定された日付が月の何日目にあたるかを返します。出力は1から31の間の整数です。\n\n【date】指定された日付。\n例えば、1はその日付が月の1日目にあたることを意味します。',
      example: 'DAY("2024.10.01")\n=>1\n\nDAY({完了日})\n=>5',
      name: 'Day',
    },
    encode_url_component: {
      description:
        'テキストをURL形式にエンコードします。\n\n【component_string】はエンコードするテキストです。以下の文字はエンコードされません：- _ . ~\n\n例えば、最初の例の出力値をブラウザのアドレスバーにコピーすると、「リンゴ」を検索するURLになります。',
      example: '"https://www.Google.com/" & ENCODE_URL_COMPONENT（{検索キーワード}）',
      name: 'Encode URL Component',
    },
    error: {
      description:
        'セルにエラーメッセージと理由を表示します。\n\n関数内にエラーの理由を説明するテキストを入力できます。例では「統計エラー」が定義されたエラー理由です。',
      example: 'IF({年齢}< 0, ERROR("統計エラー"), "正常")\n=>  #Error: 統計エラー',
      name: 'Error',
    },
    even: {
      description:
        '絶対値が増加する方向で最も近い偶数を返します。\n\n【value】は偶数に丸める数値です。\n【絶対値が増加】とは、0（ゼロ）から遠ざかる方向に値を返すことを意味します。',
      example: 'EVEN(1.5)\n=> 2\n\nEVEN(-1.8)\n=> -2',
      name: 'Even',
    },
    example: '例',
    exp: {
      description:
        'e の指定されたべき乗を返します。\n\n【e】は自然数で、約 2.718282 です。\n【power】は指数です。つまり、e のべき乗です。',
      example: 'EXP(1)\n=> 2.72\n\nEXP(2)\n=> 7.40',
      name: 'Exp',
    },
    false: {
      description:
        '論理値の偽（false）を返します。\n\nチェックボックスタイプのフィールドのセルが「未選択」状態かどうかを判断できます。例1を参照してください。\n\nFALSE()と一緒に使用して、真と偽のブール値を出力できます。例2を参照してください。',
      example:
        'IF({完了状態(チェック)}= FALSE(), "未完了", "完了")\n\nIF({平均成績} >60, TRUE(), FALSE())',
      name: 'False',
    },
    features_list: '数式一覧',
    find: {
      description:
        '特定のテキストが内容の中で最初に出現する位置を検索します。\n\n【stringToFind】は検索する特定のテキストです。\n【whereToSearch】はテキストを検索する内容を指定します。テキストパラメータまたはフィールド参照を入力できます。\n【startFromPosition】は任意で、内容のどの位置から検索を開始するかを指定します（数字で何番目の文字かを表します）。\n\nこの関数は、大量の内容の中で特定のテキストが出現する位置を迅速に検索できます。\n数字3を返す場合、テキストはその内容の3番目の文字に出現します。\n一致するテキストが見つからない場合、結果は0になります。\n\nこの関数はSEARCH()と似ていますが、一致する項目が見つからない場合、SEARCH()は空値を返し、0ではありません。',
      example:
        'FIND("リンゴ", "このリンゴは大きくて丸い、2キロのリンゴを買いますか？")\n=> 3\n\nFIND("バナナ", "このリンゴは大きくて丸い、2キロのリンゴを買いますか？")\n=> 0\n\nFIND("リンゴ", "このリンゴは大きくて丸い、2キロのリンゴを買いますか？", 10)\n=> 13',
      name: 'Find',
    },
    floor: {
      description:
        '数値を指定された基数の最も近い倍数に切り捨てます。\n\n【value】は切り捨てる値です。\n【significance】は任意で、切り捨てに使用する基数です。返される値は基数の倍数です。指定しない場合、デフォルトは1です。\n【切り捨て】とは、元の数値以下で最も近い基数の倍数に値を返すことを意味します。',
      example: 'FLOOR(1.01, 0.1)\n=> 1.0\n\nFLOOR(-1.99, 0.1)\n=> -2.0',
      name: 'Floor',
    },
    from_now: {
      description:
        '現在の日付と指定された日付の間の差を返します（正負なし）。\n\n【date】は指定された日付で、指定された日付から現在の日付までの差を計算します（カスタム時間単位）、正負なし。\n【units】は時間単位で、指定された日付と現在の日付の差を計算する単位です。たとえば、「日」で計算することも、「年」に変換することもできます。\n\n時間単位には以下の記号が含まれ、2つの形式のいずれかを使用できます：\n「単位説明符」→「略語」\nミリ秒："milliseconds" → "ms"\n秒："seconds" → "s"\n分："minutes" → "m"\n時間："hours" → "h"\n日："days" → "d"\n週："weeks" → "w"\n月："months" → "M"\n四半期："quarters" → "Q"\n年："years" → "y"\n以下のリンクをクリックして、すべての時間単位を確認できます。',
      example: 'FRONOW("2023-08-10", "y")\n=> 1\n\nFROMNOW({開始日}, "days")\n=> 25',
      name: 'From Now',
    },
    hour: {
      description:
        '指定された日付の時間を返します。出力は0（午前12時）から23（午後11時）の間の整数です。\n\n【date】指定された日付。\n例えば、18は18:00を意味します。',
      example: 'HOUR({打刻時間})\n=> 9',
      name: 'Hour',
    },
    if: {
      description:
        '特定の条件を満たすかどうかを判断し、満たす場合は最初の値を返し、満たさない場合は2番目の値を返します。\n\n【logical】は論理条件で、計算結果が真（true）または偽（false）である式を表します。\n【value1】は論理条件が真の場合の戻り値です。\n【value2】は論理条件が偽の場合の戻り値です。\n\nIFはネストして使用でき、セルが空白かどうかをチェックするためにも使用できます。',
      example:
        'IF({スコア} > 60, "合格", "不合格")\n\nIF({水温} >  40, IF({水温} < 60, "ちょうどいい", "熱すぎる"), "冷たい")\n\nIF({日付} = BLANK(), "日付を入力してください", "日付が入力されました")',
      name: 'If',
    },
    input_formula: '関数を入力',
    int: {
      description:
        '数値を最も近い整数に切り捨てます。\n\n【value】は切り捨てる値です。\n【切り捨て】とは、元の数値以下の値を返すことを意味します。',
      example: 'INT(1.99)\n=> 1\n\nINT(-1.99)\n=> -2',
      name: 'Int',
    },
    is_after: {
      description:
        '日付1が日付2より後かどうかを比較し、後であれば真（true）を返し、そうでなければ偽（false）を返します。\n\n【date1】日付1。\n【date2】日付2。\n\n日付は入力パラメータとして使用できます（例1参照）；\n日付型のフィールドを参照することもできます（例2参照）。\n\nセル内の真と偽は「チェック済み」と「未チェック」で表示されます。',
      example:
        'IS_AFTER("2024-10-02" , "2024-10-01")\n=> TRUE\n\nIS_AFTER({締切時間}, TODAY())\n=> TRUE\n\nIS_AFTER({締切時間}, "2024-10-01")\n=> TRUE',
      name: 'Is After',
    },
    is_before: {
      description:
        '日付1が日付2より前かどうかを比較し、前であれば真（true）を返し、そうでなければ偽（false）を返します。\n\n【date1】日付1。\n【date2】日付2。\n\n日付は入力パラメータとして使用できます（例1参照）；\n日付型のフィールドを参照することもできます（例2参照）。\nセル内の真と偽は「チェック済み」と「未チェック」で表示されます。',
      example:
        'IS_BEFORE("2024-10-01" , "2024-10-02")\n=> TRUE\n\nIS_BEFORE({締切時間}, TODAY())\n=> TRUE\n\nIS_BEFORE({締切時間}, "2024-10-01")\n=> TRUE',
      name: 'Is Before',
    },
    is_error: {
      description:
        '式がエラーを返すかどうかをチェックし、エラーの場合は真（true）を返します。\n\n【expr】はチェックする値です。チェックする値は算術演算、論理判断などの式である可能性があります。',
      example: 'IS_ERROR(2/0)\n=> TRUE\n\nIS_ERROR("ハハ"*2)\n=> TRUE',
      name: 'Is Error',
    },
    is_same: {
      description:
        '日付1が日付2と等しいかどうかを確認し、等しければ真（true）を返し、そうでなければ偽（false）を返します。\n\n【date1】日付1。\n【date2】日付2。\n【units】任意、比較する時間単位。例えば、2つの日付が等しいかどうかを分単位まで比較します。\n\n日付は入力パラメータとして使用できます（例1参照）；\n日付型のフィールドを参照することもできます（例4参照）。\nセル内の真と偽は「チェック済み」と「未チェック」で表示されます。\n\n以下のリンクをクリックすると、すべての計時単位を確認できます。',
      example:
        'IS_SAME("2024-10-01" , "2024-10-01")\n=> TRUE\n\nIS_SAME("2024-10-01" , "2024-11-11", "years")\n=> TRUE\n\nIS_SAME("2024-10-01" , "2024-11-11", "months")\n=> FALSE\n\nIS_SAME({締切時間}, {完了時間}, "days")\n=> TRUE\n\nIS_SAME("2024-10-01", {完了時間}, "days")\n=> TRUE',
      name: 'Is Same',
    },
    last_modified_time: {
      description:
        '各行のセルが最後に変更された時間を返します。\n注意：システムは計算列のセルの変更時間のみを返します。\n\n特定のフィールドのセルの更新時間にのみ関心がある場合は、1つ以上の列を指定できます。例2および例3を参照してください。',
      example:
        'LAST_MODIFIED_TIME()\n=> "2024-06-10 6:27 PM."\n\nLAST_MODIFIED_TIME({プロジェクト進捗})\n=> "2024-06-09 1:27 AM"\n\nLAST_MODIFIED_TIME({プロジェクト進捗}, {タスク割り当て})\n=> "2024-06-09 1:27 AM',
      name: 'Last Modified Time',
    },
    left: {
      description:
        'テキストの先頭から複数の文字を抽出します。\n\n【string】は抽出する文字のテキストです。\n【howMany】は抽出する文字の数です。数字で表します。例えば「3」は左から右に3文字を抽出することを意味します。',
      example: 'LEFT("Bika：APIをサポート、自由にDIY", 4)\n=> Bika\n\nLEFT({生年月日}, 4)\n=> 1994',
      name: 'Left',
    },
    len: {
      description:
        'テキストの文字数をカウントします。\n\n【string】は文字数をカウントするテキストです。句読点やスペースも1文字としてカウントされます。',
      example: 'LEN("どれくらい長いか当ててみて？")\n=> 8\n\nLEN("a blank")\n=> 7',
      name: 'Len',
    },
    log: {
      description:
        '指定された基数で数値の対数を返します。\n\n【number】は対数を計算する数値です。\n【base】は対数の基数です。基数が指定されていない場合、デフォルトの基数は 10 です。',
      example: 'LOG(1024, 2)\n=> 10\n\nLOG(10000)\n=> 4',
      name: 'Log',
    },
    logic: '論理',
    lower: {
      description:
        'テキスト内のすべての大文字を小文字に変換します。\n\n【string】は変換するテキストです。',
      example: 'LOWER("HELLO") => "hello"',
      name: 'Lower',
    },
    max: {
      description:
        '最大の数値を返します。\n\n【number...】は計算する数値パラメータで、数値や数値型の列を参照できます。数値型の列には、数値、通貨、パーセンテージ、評価などが含まれます。\n\nまた、この関数の入力値がすべて日付形式の場合、複数の日付の中で最も遅い日付を比較できます。',
      example: 'MAX(1, 3, 5, 7) => 7',
      name: 'Max',
    },
    mid: {
      description:
        '内容の特定の位置から固定長のテキストを抽出します。\n\n【string】は抽出するテキストを含む内容です。この内容はテキストまたはフィールドデータの参照を入力できます。\n【whereToSearch】はテキストを抽出する位置を指定します（数字で何番目の文字かを表します）。例えば、数字"3"は内容の3番目の文字から抽出を開始します。\n【count】は抽出するテキストの長さを指定します（数字で表します）。例えば、数字"2"は指定された位置から2文字を抽出します。',
      example:
        'MID("このリンゴは大きくて丸い", 3, 2)\n=> リンゴ\n\nMID("このリンゴは大きくて丸い", 99, 2)\n=> 空値\n\nMID("このリンゴは大きくて丸い", 3, 99)\n=> リンゴは大きくて丸い\n\nMID({ゲストの名前}, 2, 99)\n=> 彦祖',
      name: 'Mid',
    },
    min: {
      description:
        '最小の数値を返します。\n\n【number…】は計算する数値パラメータで、数値や数値型の列を参照できます。数値型の列には、数値、通貨、パーセンテージ、評価などが含まれます。\n\nまた、この関数の入力値がすべて日付形式の場合、複数の日付の中で最も遅い日付を比較できます。',
      example: 'MIN({数学の成績}, {英語の成績}, {国語の成績}) => 80',
      name: 'Min',
    },
    minute: {
      description:
        '指定された日付の分を返します。出力は0から59の間の整数です。\n\n【date】指定された日付。',
      example: 'MINUTE({打刻時間})\n=>30',
      name: 'Minute',
    },
    mod: {
      description:
        '2つの数値を除算した余りを返します。\n\n【value】は被除数です。\n【divisor】は除数です。\n\n結果の符号は除数の符号と同じです。',
      example: 'MOD(10, 3) => 1',
      name: 'Mod',
    },
    month: {
      description:
        '指定された日付に対応する月を返します。\n\n【date】指定された日付。\n\nこの関数の出力値は1（1月）から12（12月）の間の整数です。',
      example: 'MONTH("2024.10.01")\n=> 10\n\nMONTH({卒業時間})\n=> 6',
      name: 'Month',
    },
    not: {
      description:
        '引数の論理値を反転します。\n\n【boolean】はブール引数で、入力値は論理判断であり、出力値は真または偽のみです。たとえば、2つの値の大小を比較します。\n引数の論理判断が真（true）の場合、関数は偽（false）を返します。\n引数の論理判断が偽（false）の場合、関数は真（true）を返します。\n\n例1：2>3の出力値は偽ですが、反転後の関数の出力値は真です。\n例2：NOT({年齢} > 18)は、NOT関数で反転後、実際には{年齢} ≤ 18を判断します。',
      example: 'NOT({年齢} > 18)',
      name: 'Not',
    },
    now: {
      description:
        '今日の日付と時間を返し、時分秒まで精密です。\n\nこの関数を直接使用して年月日を返すことができます（例1参照）。\n\nまた、DATEADDやDATETIME_DIFFなどの関数と組み合わせて使用することもできます。たとえば、{締め切り時間}から現在の時間を引いて、プロジェクトのカウントダウンを表示します（例2参照）。\n\n注意：計算式を再計算するか、表を更新するたびに、この関数の結果が更新されます。',
      example:
        'NOW()\n=> "2024/06/02 07:12"\n\nDATETIME_DIFF( {締め切り時間} , NOW(),"days")\n=> 15',
      name: 'Now',
    },
    number: '数',
    object: 'オブジェクト',
    odd: {
      description:
        '絶対値が増加する方向で最も近い奇数を返します。\n\n【value】は奇数に丸める数値です。\n【絶対値が増加】とは、0（ゼロ）から遠ざかる方向に値を返すことを意味します。',
      example: 'ODD(1.5)\n=> 3\n\nODD(-2.1)\n=> -3',
      name: 'Odd',
    },
    or: {
      description:
        'いずれかのパラメータが真（true）である場合、真（true）を返し、それ以外の場合は偽（false）を返します。\n\n【logical】は論理パラメータで、論理値、配列、またはフィールドの参照である可能性があります。',
      example: 'OR(3>2, 2>3)\n=>  true',
      name: 'Or',
    },
    power: {
      description:
        '指定された基数のべき乗を返します。つまり、基数を指定された指数で累乗します。\n\n【base】は基数です。\n【power】は指数です。',
      example: 'POWER(2, 5)\n=> 32\n\nPOWER(-5, 3)\n=> -125',
      name: 'Power',
    },
    record_id: {
      description: 'レコードのIDを返します',
      example: '"https://awesomeservice.com/view?recordId=" & RECORD_ID()',
      name: 'Record ID',
    },
    replace: {
      description:
        '内容の特定の位置にあるテキストを新しいテキストに置き換えます。\n\n【string】は置き換えるテキストを含む内容です。この内容はテキストまたはフィールドデータの参照を入力できます。\n【start_character】はテキストを置き換える位置を指定します（数字で何番目の文字かを表します）。例えば、数字"3"は内容の3番目の文字から置き換えを開始します。\n【number_of_characters】は置き換える文字数を指定します（数字で表します）。例えば、数字"2"は指定された位置から2文字を置き換えます。\n【replacement】は元のテキストを置き換える新しいテキストです。\n\n（内容中のすべての出現箇所を新しいテキストに置き換える場合は、SUBSTITUTEを参照してください。）',
      example:
        'REPLACE("このリンゴは大きくて丸い", 3, 2, "桃")\n=> この桃は大きくて丸い\n\nREPLACE("このリンゴは大きくて丸い", 3, 99, "ドリアンは香りが良くて甘い")\n=> このドリアンは香りが良くて甘い\n\nREPLACE({ゲストの名前}, 1, 1, "X")\n=> X彦祖',
      name: 'Replace',
    },
    rept: {
      description:
        '指定された回数だけテキストを繰り返します。\n\n【string】は繰り返すテキストです。\n【mumber】は指定された繰り返し回数です。数字で表します。例えば「2」は2回繰り返すことを意味します。',
      example: 'REPT("ハ", 2)\n=> ハハ',
      name: 'Rept',
    },
    right: {
      description:
        'テキストの末尾から複数の文字を抽出します。\n\n【string】は抽出する文字のテキストです。\n【howMany】は抽出する文字の数です。数字で表します。例えば「5」は右から左に5文字を抽出することを意味します。',
      example:
        'RIGHT("Bika：APIをサポート、自由にDIY", 5)\n=> 自由にDIY\n\nRIGHT({生年月日}, 5)\n=> 07-13',
      name: 'Right',
    },
    round: {
      description:
        '指定された桁数に従って数値を四捨五入します。\n\n【value】は四捨五入する値です\n【precision】は任意で、四捨五入する桁数です。指定しない場合、デフォルトは1です。\n\n桁数が0より大きい場合、指定された小数位に四捨五入されます。\n桁数が0の場合、最も近い整数に四捨五入されます。\n桁数が0より小さい場合、小数点の左側で四捨五入されます。',
      example: 'ROUND(3.14159, 2) => 3.14',
      name: 'Round',
    },
    rounddown: {
      description:
        '指定された桁数に従って数値を絶対値が小さくなる方向に切り捨てます。\n\n【value】は切り捨てる値です。\n【precision】は任意で、数値を切り捨てる桁数です。指定しない場合、デフォルトは1です。\n【絶対値が小さくなる】とは、0（ゼロ）に近づく方向に値を返すことを意味します。\n\n桁数が0より大きい場合、指定された小数位に切り捨てます。\n桁数が0の場合、最も近い整数に切り捨てます。\n桁数が0より小さい場合、小数点の左側で切り捨てます。',
      example: 'ROUNDDOWN(3.14159, 2) => 3.14',
      name: 'Round Down',
    },
    roundup: {
      description:
        '指定された桁数に従って絶対値が増加する方向に数値を切り上げます。\n\n【value】は切り上げる値です。\n【precision】は任意で、数値を切り上げる桁数です。指定しない場合、デフォルトは1です。\n【絶対値が増加】とは、0（ゼロ）から遠ざかる方向に値を返すことを意味します。\n\n桁数が0より大きい場合、指定された小数位に四捨五入されます。\n桁数が0の場合、最も近い整数に四捨五入されます。\n桁数が0より小さい場合、小数点の左側で四捨五入されます。',
      example: 'ROUNDUP(3.14159, 2) => 3.15',
      name: 'Round Up',
    },
    search: {
      description:
        '特定のテキストが内容の中で最初に出現する位置を検索します。\n\n【stringToFind】は検索する特定のテキストです。\n【whereToSearch】はテキストを検索する内容を指定します。テキストパラメータまたはフィールド参照を入力できます。\n【startFromPosition】は任意で、内容のどの位置から検索を開始するかを指定します（数字で何番目の文字かを表します）。\n\nこの関数は、大量の内容の中で特定のテキストが出現する位置を迅速に検索できます。\n数字3を返す場合、テキストはその内容の3番目の文字に出現します。\n一致するテキストが見つからない場合、結果は空値になります。\n\nこの関数はFIND()と似ていますが、一致する項目が見つからない場合、FIND()は0を返し、空値ではありません。',
      example:
        'SEARCH("リンゴ", "このリンゴは大きくて丸い、2キロのリンゴを買いますか？")\n=> 3\n\nSEARCH("バナナ", "このリンゴは大きくて丸い、2キロのリンゴを買いますか？")\n=> 空値\n\nSEARCH("リンゴ", "このリンゴは大きくて丸い、2キロのリンゴを買いますか？", 10)\n=> 13',
      name: 'Search',
    },
    second: {
      description:
        '指定された日付の秒を返します。出力は0から59の間の整数です。\n\n【date】指定された日付。',
      example: 'SECOND({打刻時間})\n=> 1',
      name: 'Second',
    },
    select_a_formula: 'フィールドまたは関数を選択',
    set_locale: {
      description:
        '指定された日付と時刻に特定のロケールを設定します。\n\n【date】指定された日付。\n【locale_modifier】ロケール指定子。\n\nこの関数はDATETIME_FORMATと一緒に使用する必要があります。サポートされているロケール指定子は以下のリンクをクリックして確認できます。',
      example: 'DATETIME_FORMAT(SET_LOCALE(NOW(), "zh-cn"), "lll")\n=> 2024年6月2日午前11時04分',
      name: 'Set Locale',
    },
    set_timezone: {
      description:
        '指定された日付に特定のタイムゾーンを設定します。\n\n【date】指定された日付。\n【tz_identifier】タイムゾーン指定子。例えば、"8"はUTC+8を、"-2"はUTC-2を意味します。\n\nこの関数はDATETIME_FORMATと一緒に使用する必要があります。',
      example: 'DATETIME_FORMAT(SET_TIMEZONE(NOW(), -8), "M/D/YYYY h:mm")\n=> 9/20/2024 2:30',
      name: 'Set Timezone',
    },
    sqrt: {
      description:
        '数値の平方根を返します。\n\n【value】は平方根を求める数値です。\n\n数値が負の場合、SQRT は NaN を返します。',
      example: 'SQRT(16) => 4',
      name: 'Sqrt',
    },
    substitute: {
      description:
        '内容の中で特定のテキストを新しいテキストに置き換えます。\n\n【string】は置き換えるテキストを含む内容です。この内容はテキストまたはフィールドデータの参照を入力できます。\n【old_text】は置き換えるテキストです。\n【new_text】は新しいテキストです。\n【instance_num】は任意で、置き換えるインスタンスの番号を指定します。指定しない場合、すべてのインスタンスが置き換えられます。',
      example:
        'SUBSTITUTE("小胡、小張、小王", "小", "老")\n=> 老胡、老張、老王\n\nSUBSTITUTE("小胡、小張、小王", "小", "老", 3)\n=> 小胡、老張、小王',
      name: 'Substitute',
    },
    sum: {
      description:
        'すべての数値を合計します。\n      【number...】は計算する数値パラメータで、数値や数値型の列を参照できます。\n      数値型の列には、数値、通貨、パーセンテージ、評価などが含まれます',
      example: 'SUM(1, 3, 5, "", "VI") => 1 + 3 + 5 = 9',
      name: 'Sum',
    },
    switch: {
      description:
        'この関数は複数の分岐選択関数であり、式と複数の分岐+戻り値で構成されます。式が特定の分岐値に等しい場合、関数はその分岐に対応する戻り値を出力します。\n\n【expression】は式で、その計算結果が各分岐と一致します。\n【pattern】は分岐で、各分岐は式の可能な計算結果を表します。各分岐には対応する戻り値があります。\n【result】は戻り値で、式の計算結果が分岐と一致した場合、その対応する戻り値を出力します。\n【default】はデフォルト値で、計算結果がどの分岐とも一致しない場合、関数はデフォルト値を出力します。デフォルト値が記入されていない場合は空値になります。\n\n例えば、例1では、{国}はデータの列を参照しており、その出力値は数千の国名になる可能性があります。これはこの関数の式です。「中国」と「中国語」はそれぞれ分岐と戻り値であり、{国}の出力値が「中国」の場合、「中国語」を返します。「共通英語」はデフォルト値であり、{国}の出力値がどの分岐とも一致しない場合、「共通英語」を出力します。',
      example:
        'SWITCH({国}, "中国", "中国語", "ロシア", "ロシア語", "フランス", "フランス語", "日本", "日本語", "共通英語")\n\nSWITCH("C", "A", "優秀", "B", "中等", "C", "普通", "D", "劣る", "成績なし")\n=>普通',
      name: 'Switch',
    },
    t: {
      description:
        '入力値がテキスト型の場合、元のテキストを返し、非テキスト型の場合は空値を返します。\n\n【value】はテキストかどうかをチェックする値です。\n\n例えば、入力値が数字や日付などのフィールドを参照している場合、空値が返されます。',
      example:
        'T("Bika")\n=> Bika\n\nT("55")\n=> 55\n\nT(55)\n=> 空値\n\nT({数学の成績})\n=> 空値    ',
      name: 'T',
    },
    text: 'テキスト',
    timestr: {
      description:
        '日付を「時:分:秒」形式のテキストにフォーマットします（固定フォーマットはHH:mm:ss）\n\n【date】フォーマットする日付\n\n日付がフォーマットされると、テキストの文字列になり、日付データの属性を持たなくなります。',
      example: 'TIMESTR(NOW())\n=> "04:52:12',
      name: 'Timestr',
    },
    to_now: {
      description:
        '現在の日付と指定された日付の間の差を返します（正負なし）。\n\n【date】は指定された日付で、指定された日付から現在の日付までの差を計算します（カスタム時間単位）、正負なし。\n【units】は時間単位で、指定された日付と現在の日付の差を計算する単位です。たとえば、「日」で計算することも、「年」に変換することもできます。\n\n時間単位には以下の記号が含まれ、2つの形式のいずれかを使用できます：\n「単位説明符」→「略語」\nミリ秒："milliseconds" → "ms"\n秒："seconds" → "s"\n分："minutes" → "m"\n時間："hours" → "h"\n日："days" → "d"\n週："weeks" → "w"\n月："months" → "M"\n四半期："quarters" → "Q"\n年："years" → "y"\n以下のリンクをクリックして、すべての時間単位を確認できます。',
      example: 'TONOW("2023-08-10", "y")\n=> 1\n\nTONOW({開始日}, "days")\n=> 25',
      name: 'To Now',
    },
    today: {
      description:
        '今日の日付（年月日）を返しますが、時分秒までは精密ではありません（デフォルトは00:00:00）。時分秒まで精密にしたい場合は、関数NOWを使用してください。\n\nこの関数を直接使用して年月日を返すことができます（例1参照）。\nまた、DATEADDやDATETIME_DIFFなどの関数と組み合わせて使用することもできます。たとえば、{締め切り時間}から現在の時間を引いて、プロジェクトのカウントダウンを表示します（例2参照）。\n\n注意：計算式を再計算するか、表を更新するたびに、この関数の結果が更新されます。',
      example: 'TODAY() => 2024/06/02',
      name: 'Today',
    },
    trim: {
      description: 'テキストの先頭と末尾の空白を削除します。\n\n【value】は処理するテキストです。',
      example: 'TRIM(" 両端の空白が削除されます! ")\n=>両端の空白が削除されます!',
      name: 'Trim',
    },
    true: {
      description:
        '【概要】\n論理値の真（true）を返します。\n\n【パラメータ説明】\nこの関数はパラメータを入力する必要はありません。\nこの関数はチェックボックスタイプのフィールドが「選択済み」状態かどうかを判断できます。例1を参照してください。\nこの関数はFALSE()と一緒に使用して、真と偽のブール値を出力できます。例2を参照してください。',
      example:
        '// チェックボックスタイプのフィールドの状態を判断します。例えば、以下の{完了状態}フィールドがチェックボックスタイプで、セルの値が「選択済み」の場合：\n式：IF({完了状態} = TRUE(), "完了", "未完了")\n計算結果："完了"\n\n// TRUE()とFALSE()を一緒に使用してブール値を出力します。例えば、以下の{成績}フィールドが数値型で、セルの値が70の場合：\n式：IF({成績} > 60, TRUE(), FALSE())\n計算結果：true',
      name: 'True',
    },
    upper: {
      description:
        'テキスト内のすべての小文字を大文字に変換します。\n\n【string】は変換するテキストです。',
      example: 'UPPER("hello") => "HELLO"',
      name: 'Upper',
    },
    usage: '使用法',
    value: {
      description:
        'テキスト文字列を数値に変換します。\n\n【text】は変換するテキスト値です。\n\nこの関数はテキスト内の数値を抽出できます。',
      example: 'VALUE("$10000")\n=> 10000',
      name: 'Value',
    },
    weekday: {
      description:
        '指定された日付に対応する曜日を返します。\n\n【date】指定された日付。\n【startDayOfWeek】任意、週の開始日です。デフォルトでは、各週は日曜日から始まります（つまり、日曜日は0）。開始日を"Monday"（月曜日、例2参照）に設定することもできます。\n\nこの関数の出力値は0から6の間の整数です。 ',
      example:
        'WEEKDAY("2024.10.01")\n=>4\n\nWEEKDAY("2024.10.01", "Monday")\n=>3\n\nWEEKDAY(TODAY())',
      name: 'Weekday',
    },
    weeknum: {
      description:
        '指定された日付が年の第何週目にあたるかを返します。\n\n【date】指定された日付。\n【startDayOfWeek】任意、一週間の開始日。デフォルトでは、各週は日曜日から始まります（つまり、日曜日は0）。開始日を"Monday"（月曜日）に設定することもできます。\n\nこの関数の出力は整数です。例えば、6はその日付が年の第6週目にあたることを意味します。',
      example:
        'WEEKNUM("2024.10.01")\n=>40\n\n\nWEEKNUM("2024.10.01", "Sunday")\n=>40\n\nWEEKNUM(TODAY())\n=>33',
      name: 'Weeknum',
    },
    workday: {
      description:
        '開始日から指定された営業日数後の日付を返します。\n\n【startDate】は指定された開始日です。\n【numDays】は開始日から指定された営業日数を表し、正の数値を使用します。たとえば、数値「1」は開始日から1営業日後の日付を表します（例1参照）。\n【holidays】は任意です。カレンダーから除外する特定の日付を指定します。たとえば、祝日です。入力形式は「yyyy-mm-dd」で、複数の日付はカンマで区切ります（例3参照）。\n\nこの関数の営業日には週末と指定された特定の日付は含まれません。',
      example:
        'WORKDAY("2024/10/01" , 1)\n=> 2024/10/02\n\nWORKDAY("2024/10/01" , 1，"2024-10-02")\n=> 2024/10/05\n\nWORKDAY({開始日}, 100, "2024-10-01, 2024-10-02, 2024-10-03, 2024-10-04, 2024-10-05, 2024-10-06, 2024-10-07, 2024-10-08")\n=> 2024-11-11',
      name: 'Workday',
    },
    workday_diff: {
      description:
        '2つの日付の間に何営業日があるかをカウントします（正負あり）。\n\n【startDate】開始日。\n【endDate】終了日。開始日が終了日より遅い場合、負の数が表示されます。\n【holidays】任意。カレンダーから除外する日付（例：祝日）。入力形式は「yyyy-mm-dd」で、複数の日付はカンマで区切ります。\n\nこの関数は開始日と終了日の間の営業日をカウントし、週末と指定された特定の日付を含みません。',
      example:
        'WORKDAY_DIFF("2024-10-01", "2024-10-02")\n=> 2\n\nWORKDAY_DIFF("2024-10-02", "2024-10-01")\n=> -2\n\nWORKDAY_DIFF("2024-10-01", "2024-10-05")\n=> 3\n\nWORKDAY_DIFF({製品開始日}, {製品リリース日} , "2024-06-25, 2024-06-26, 2024-06-27")\n=> 100',
      name: 'Workday Diff',
    },
    xor: {
      description:
        '奇数個の引数が真（true）の場合は真（true）を返し、それ以外の場合は偽（false）を返します。\n\n【logical】は論理引数で、論理値、配列、または参照フィールドを指定できます。',
      example: 'XOR(3>2, 2>3, 4>3)\n=> false',
      name: 'Xor',
    },
    year: {
      description: '指定された日付に対応する4桁の年を返します。\n\n【date】指定された日付。',
      example: 'YEAR("2024/10/01")\n=> 2024\n\nYEAR({卒業時間})\n=> 2024',
      name: 'Year',
    },
  },
  global: {
    action: {
      cannot_be_empty: '空にできません',
      detail: '詳細',
      full_screen: '全画面',
      no_result_found: '検索履歴なし',
      preview: 'プレビュー',
      select: '選択',
      toggle: '切り替え',
      un_named: '無名の',
      zoom_in: 'ズームイン',
      zoom_out: 'ズームアウト',
    },
    copilot: {
      delete_history: '対話を削除',
      delete_history_confirm: 'この対話履歴を削除してもよろしいですか？',
      history: '会話履歴',
      history_empty: '対話履歴はありません',
      history_loading: '読み込み中...',
      history_no_description: '説明なし',
      history_no_more: 'これ以上ありません',
      history_no_title: 'タイトルなし',
      new_chat: '新しいチャット',
      node_resource: 'ノードリソース',
      title: 'AI アシスタント',
      upload_file: 'ファイルをアップロード',
      welcome: 'こんにちは、AIアシスタントです。お手伝いが必要な時はいつでもご利用ください！',
    },
    dl_link_unavailable: 'ダウンロードリンクが利用できません',
    download: 'ダウンロード',
    error_description:
      '技術チームに通知されており、問題解決に努めています。以下の操作をお試しください:',
    error_reason: [
      '前のページに戻ってリフレッシュし、もう一度試してください。',
      '一時的に離れて、後で再試行してください。',
      '問題が解決しない場合は、カスタマーサポートに連絡してさらにサポートを受けてください。',
    ],
    guest: 'ゲスト',
    guest_management: 'ゲスト管理',
    hooks: {
      firebase: {
        create_hardware_description: 'ハードウェアデバイスの統合バインディング',
        create_hardware_name: 'ハードウェアデバイスのバインディング',
      },
    },
    me: '私(現在の訪問者)',
    page_not_found: '申し訳ありませんが、訪問したページが見つかりませんでした',
    page_not_found_description:
      'リクエストされたページが見つかりませんでした。以下の理由が考えられます:',
    page_not_found_reason: [
      '入力したURLが間違っているか、スペルミスがある',
      'リクエストされたページが削除されるか移動された',
      'サーバーが一時的にリクエストされたリソースを見つけられない',
    ],
    retry: 'リトライ',
    select: '選択してください',
    select_path: '完全なパスを選択してください',
    server_error: '申し訳ありませんが、リクエストを処理中にサーバーが問題に直面しました',
    toast: {
      description_update_success: '説明を更新しました',
      open_in_database: 'データバースページを開きます',
    },
    welcome: '{name}へようこそ',
  },
  grid: {
    asc_option: 'オプションで昇順',
    batch_update_selected_record: '一括更新 {recordCount} 件のレコード',
    bulk_update: '一括更新',
    bulk_update_confirm_content: '{count} 件のレコードが変更されます。保存してもよろしいですか？',
    bulk_update_confirm_title: '一括更新の確認',
    bulk_update_fields: '更新するフィールド',
    bulk_update_successful: '一括更新レコードが成功しました',
    bulk_update_title: 'レコードの一括更新',
    copy_row: '行をコピー',
    created_doc: 'ドキュメントを作成',
    delete_n_record: '{count} 件のレコードを削除',
    desc_option: 'オプションで降順',
    duplicate_record: '重複するレコード',
    edit_record: 'レコードを編集',
    filter_to_find_all_records: 'データベース内の一括編集が必要なレコードをフィルターで検索',
    group: 'グループ',
    lookup_unamed_record: '無名の記録',
    new_record: '新規レコード',
    pasting_multiple_columns_is_not_supportted_currently:
      '複数列の貼り付けは現在サポートされていません',
    record_selected: '{count}件のレコードが選択されました',
    row_group: 'グループ分け',
    see_more_detail: 'もっと見る',
    select_record_by_name: '選択 {name}',
    tooltip_new_record: '新しいレコードを作成するにはクリックしてください',
    un_named_doc: '無名のドキュメント',
  },
  help: {
    description: 'もっと知りたいですか？まだ助けが必要ですか？',
    help: 'ヘルプ',
    help_and_support: 'ヘルプとサポート',
    help_center: 'ヘルプセンター',
    title: 'お手伝いしましょうか？',
  },
  integration: {
    advertise: {
      can_do: '以下は参考になるいくつかの具体的な使用例です:',
      connect_to: '{name}に接続',
      notice:
        '注意: このアプリは、お客様のアカウントのすべてのユーザーが利用できます。このアプリをインストールすることで、その利用規約に同意したものとみなされます。',
    },
    airtable: {
      airtable_token: 'Airtable API Token',
      airtable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'Airtable 統合により、ユーザーは Airtable のフォームデータをシステムに直接同期できます。自動化フローにより、フォームデータが更新されるとデータベースの更新、通知の送信、レポートの生成などの関連操作が自動的にトリガーされます。リアルタイムのデータ同期と管理を必要とするビジネスシナリオに適しています。',
      title: 'Airtable',
    },
    aitable: {
      aitable_token: 'AITable API Token',
      aitable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'AITable 統合により、ユーザーは AITable のデータをシステムに統合できます。データの変更時に自動的にデータの同期、通知のプッシュ、レポートの生成などのタスクをトリガーする自動化フローと組み合わせることができます。効率的なデータ管理とリアルタイムの対応を必要とするシナリオに適しています。',
      title: 'AITable',
    },
    apitable: {
      apitable_token: 'APITable API Token',
      apitable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'APITable 統合により、ユーザーは APITable のデータをシステムとシームレスに接続できます。データの変更時に自動的にレコードの更新、リマインダーの送信、他の操作をトリガーする自動化フローを利用できます。柔軟なデータ管理と迅速な対応を必要とするアプリケーションに適しています。',
      title: 'APITable',
    },
    awsocr: {
      aws_ocr_token: 'AWS Textract API Token',
      aws_ocr_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'AWS Textract は、AWS の光学文字認識サービスの接続と管理に使用され、画像の文字認識と抽出をサポートし、ユーザーがテキストデータを効率的に処理および保存できるようにします。',
      form_item_1_label: 'API キー',
      form_item_1_placeholder: 'API キーを入力してください',
      title: 'AWS Textract',
    },
    azure: {
      apikey: 'Azure AI API キー',
      apikey_placeholder: 'Azure AI の API キーを入力してください',
      description:
        'API キー認証で Azure AI（Azure OpenAI／Azure AI Agents）を利用し、完成生成、チャット、埋め込み、エージェント展開などにアクセスします。',
      title: 'Azure AI',
    },
    banner_description: '連携を管理して、ビジネスプロセスを自動化します',
    banner_title: '連携センター',
    bedrock: {
      apikey: 'Amazon Bedrock API キー',
      apikey_placeholder: 'Amazon Bedrock の API キーを入力してください',
      description:
        'Amazon Bedrock を通じて Foundation Models を呼び出し、ファインチューニング、知識ベース強化、タスクエージェントなど強力な生成 AI 機能を実現します。',
      title: 'Amazon Bedrock',
    },
    byte_doubao: {
      description: 'バイト豆包の統合、インテリジェントな会話とコンテンツ生成を提供します。',
      title: 'バイト豆包',
      use_cases: [
        '学生の学科質問や論文作成ガイドを提供',
        '職場でのレポート作成や専門的なアドバイスをサポート',
        '旅行計画、エンターテインメント推奨、健康相談を提供',
        'クリエイターにインスピレーションやデザイン提案を提供',
      ],
    },
    claudeai: {
      description:
        'Claude 系列模型は Anthropic 公司が開発した大言語モデル（Haiku、Sonnet、Opus を含む）で、知的な会話、コンテンツ生成、データ分析を提供します。複雑なクエリを理解し、正確な回答を提供し、ユーザーの作業効率と創造力を向上させます。',
      title: 'Claude.ai',
      use_cases: [
        'データテーブルのレコードを集計してレポートを生成',
        'データテーブルのレコードに基づいてメール本文を生成',
        'ブログ記事、プレスリリースなどのテキストコンテンツを生成',
      ],
    },
    deepseek: {
      apikey: 'DeepSeek APIキー',
      apikey_help_text:
        'DeepSeekから提供されたAPIキーを入力してください。DeepSeek API Keysで見つけることができます。https://platform.deepseek.com/api_keys',
      apikey_placeholder: 'sk-xxxxxxxxxxxxxxxxxxxxxxxx',
      custom_base_url: 'カスタムベースURL',
      custom_base_url_description:
        '一部のサードパーティプラットフォームでも DeepSeek モデルの呼び出しが提供されているため、基礎 URL をカスタマイズすることができます（例：アリババクラウド、ボルケーノエンジン、シリコンバレーフロー）。',
      custom_model: 'カスタムモデルID',
      custom_model_description: '使用したいモデルを指定するためにカスタムモデルIDを使用できます。',
      description:
        'DeepSeek-R1 は、強化学習とコールドスタートデータで最適化された最先端の大規模言語モデルで、優れた推論、数学、およびコードパフォーマンスを提供します。',
      organization_id: '組織ID',
      organization_id_help_text:
        '組織IDはあなたの組織の一意の識別子であり、APIリクエストで使用できます。（通常は不要）',
      title: 'DeepSeek',
    },
    delete_warning_content: '削除後は元に戻せません。本当に削除しますか？',
    delete_warning_title: '設定を削除',
    description_know_more: 'ヘルプドキュメントを確認して、詳しく知る',
    dingtalk: {
      description:
        'DingTalk のカスタムボットの Webhook を通じて、DingTalk グループに様々なメッセージを自動的に送信します。自動化と組み合わせて、タスクリマインダー、ステータス更新、またはプロジェクトレポートに利用できます。DingTalk グループを通じてタスク完了や重要な情報を瞬時にプッシュ通知することで、企業内の内部コミュニケーションとタスク管理の効率を高めます。',
      form_item_1_label: 'Webhook URL',
      form_item_1_placeholder: 'Webhook URL',
      title: 'DingTalk カスタムボット',
    },
    explore_integration: '連携を探索する',
    features_list: 'Integrations List',
    feishu: {
      description:
        'Feishu のカスタムボットの Webhook を通じてグループにメッセージを送信します。自動化と組み合わせて、定期的な更新、アラート、または会議スケジュールを Feishu プラットフォームで自動的にプッシュできます。この統合により、チームメンバーは Feishu グループでの重要な進展について通知を受け、ワークフローの透明性と情報共有の効率を高めます。',
      form_item_1_label: 'Webhook URL',
      form_item_1_placeholder: 'Webhook URL',
      title: 'Feishu カスタムボット',
    },
    general: {
      err_msg: 'この項目は空にできません',
      note: '備考名',
      note_placeholder: '覚えやすく識別しやすい名前を付けてください',
    },
    github: {
      description:
        'BikaアカウントをGitHubアカウントとリンクさせることで、GitHubアカウントを使用してBikaに簡単にログインでき、安全かつ便利です。',
      title: 'GitHub',
    },
    google: {
      description:
        'BikaアカウントをGoogleアカウントとリンクさせることで、Googleアカウントを使用してBikaに簡単にログインでき、安全かつ便利です。',
      title: 'Google',
    },
    googleai: {
      description:
        'Google AI は、Google が開発した一連の大規模言語モデル（Gemini を含む）で、知的な会話、コンテンツ生成、データ分析を提供します。複雑なクエリを理解し、正確な回答を提供し、ユーザーの作業効率と創造力を向上させます。',
      title: 'Google AI',
      use_cases: [
        'データテーブルの記録を活用して深い市場分析を行い、ユーザーのニーズと行動パターンを洞察する',
        'データテーブルの情報を自動的に統合し、視覚化された専門的な分析レポートを生成する',
        '与えられたテーマとキーワードに基づいて、魅力的な記事やソーシャルメディアコンテンツをインテリジェントに作成する',
      ],
    },
    imap: {
      create_new_integration: '新しいメールアカウントに接続する',
      description:
        'IMAP メールアカウントを設定することで、ユーザーはメール受信機能をシステムに統合できます。自動化フローと組み合わせて、特定のメールを受信した際にタスクの自動作成、メールのアーカイブ、アラートのトリガーなどのアクションを実行できます。メールから情報を取得し、対応する必要があるシナリオに適しています。',
      password_label: 'パスワード',
      password_placeholder: 'パスワードを入力してください',
      port_err_msg: 'ポートは数値でなければなりません',
      port_helper_text:
        'IMAPサーバーが使用するポート番号を入力してください。一般的なポートは993です。',
      port_label: 'ポート',
      port_placeholder: 'IMAP サーバーが使用するポート番号を入力してください',
      server_helper_text:
        '送受信メールサーバーアドレス(IMAP)を入力してください。この情報が利用できない場合は、メールサービスプロバイダーにお問い合わせください',
      server_label: 'IMAP サーバー',
      server_placeholder: 'imap.example.com',
      title: 'IMAP メールアカウント',
      tls_label: 'TLS を有効にする',
      user_name_label: 'ユーザー名',
      user_name_placeholder: '<EMAIL>',
    },
    integration: '連携',
    linkedin: {
      description:
        'LinkedIn は、ユーザーが職業ネットワークを構築し、仕事の機会を見つけ、業界の洞察を共有するのを支援するために設計されたプロフェッショナルなソーシャルプラットフォームです。Bika の自動化機能を統合することで、会社や個人情報を効率的に管理できます。',
      title: 'LinkedIn',
      use_cases: [
        'データベースのレコードを使用して LinkedIn に新しいブログ記事を公開する',
        'データベースのレコードで LinkedIn 会社ページを更新する',
        '新しいフォームの提出に対して LinkedIn の共有更新を作成する',
      ],
    },
    make: {
      description:
        'Make.com は、自動化プラットフォームであり、コードなしまたは低コードのソリューションを通じてアプリケーションとサービスを接続し、ワークフローを簡素化します。Bika のデータベースと自動化機能を統合することで、データがプラットフォーム間でシームレスに流れることができます。',
      title: 'Make.com',
      use_cases: [
        'Make.com を使用してデータを数千のアプリに投稿する',
        'Make.com のシナリオからデータをデータベースに保存する',
        'Make.com のシナリオから新しいデータベースレコードを作成する',
        '新しいフォーム提出時に Make.com のシナリオを有効化する',
      ],
    },
    my_integration: '私の連携',
    mysql: {
      database_name: 'Database Name',
      database_name_placeholder: ' ',
      description:
        'MySQL は、MySQL データベースに接続して管理するために使用され、データのクエリ、挿入、更新、および削除をサポートし、ユーザーが効率的にデータを処理および保存できるようにします。',
      host_helper_text:
        'MySQL サーバーのアドレスを入力してください。この情報が利用できない場合は、データベース管理者にお問い合わせください。',
      host_label: 'MySQL サーバー',
      host_placeholder: 'mysql.example.com',
      name_label: 'Username',
      name_placeholder: '<EMAIL>',
      password_label: 'Password',
      password_placeholder: ' ',
      port_err_msg: 'ポートは数字である必要があります',
      port_helper_text: 'MySQL サーバーが使用するポート番号を入力してください。',
      port_label: 'Port',
      port_placeholder: ' ',
      title: 'MySQL',
    },
    openai: {
      apikey: 'OpenAI トークン',
      apikey_help_text:
        'OpenAI から提供された API キーを入力してください。OpenAI アカウント設定で確認できます。https://platform.openai.com/api-keys',
      apikey_placeholder: 'sk-xxxxxxxxxxxxxxxxxxxxxxxx',
      custom_base_url: 'カスタムベース URL',
      custom_base_url_description:
        'OpenAI のベース URL をカスタマイズできます。すべての OpenAI 互換 AI モデル API がサポートされます。 (Google Gemini、Anthropic Claude など)',
      custom_model: 'カスタムモデル Model ID',
      custom_model_description: 'OpenAI のカスタムモデル ID を入力してください。',
      description:
        'OpenAI の GPT モデルを使用すると、自然言語テキストの自動生成、インテリジェントな対話、コードスニペットの作成、またはパーソナライズされた提案の提供などが可能です。',
      organization_id: '組織 ID',
      organization_id_help_text: '組織 ID は、API リクエストで使用できる組織の一意の識別子です。',
      title: 'OpenAI',
    },
    page_description:
      '数百の他のアプリ、AIエージェント、AIモデルを統合。Bika.aiを使用して、サードパーティアプリとあなたのスタックの間で高度な自動化を作成できます。',
    page_title: '統合 | Bika.aiによるAIワークフロー自動化',
    postgresql: {
      database_name: 'Database Name',
      database_name_placeholder: ' ',
      description:
        'PostgreSQL は、PostgreSQL データベースの接続と管理に使用され、データのクエリ、挿入、更新、および削除をサポートし、ユーザーがデータを効率的に処理および保存できるようにします。',
      host_helper_text:
        'PostgreSQL サーバーのアドレスを入力してください。この情報をお持ちでない場合は、データベース管理者にお問い合わせください。',
      host_label: 'PostgreSQL Server',
      host_placeholder: 'postgresql.example.com',
      name_label: 'Username',
      name_placeholder: '<EMAIL>',
      password_label: 'Password',
      password_placeholder: ' ',
      port_err_msg: 'ポートは数字である必要があります',
      port_helper_text: 'PostgreSQL サーバーが使用するポート番号を入力してください。',
      port_label: 'Port',
      port_placeholder: ' ',
      title: 'PostgreSQL',
    },
    siri: {
      description:
        'Apple Siri、ショートカット、および Bika API を組み合わせることで、さまざまなワークフローの自動化が可能になります。例えば、ユーザーは Siri 音声コマンドを使用して、Bika ミッションを自分や同僚のために迅速に作成し、手を解放して効率を向上させることができます。',
      title: 'Siri',
      use_cases: [
        'Siri 音声コマンドとショートカットを使用してデータテーブルの記録を検索',
        'Siri とショートカットを使用してデータテーブルに新しい記録を作成',
        'Siri とショートカットを使用して写真をデータテーブルにアップロード',
        'Siri を使用して Bika アプリを開き、特定のデータテーブルを表示',
        'Siri とショートカットを使用してデータテーブルのイベントをモバイルカレンダーに同期',
      ],
    },
    slack: {
      description:
        'Slack アプリの Incoming Webhook を使用してチャンネルにメッセージを送信します。自動化と組み合わせて、タスクの完了、システムの状態更新、プロジェクトレポートなどのイベントトリガー時に通知を自動的にプッシュできます。Slack チャンネルでチームメンバーがタイムリーに通知を受け取り、チームのコラボレーションと情報伝達の効率を向上させます。',
      form_item_1_label: 'Incoming Webhook URL',
      form_item_1_placeholder: 'Incoming Webhook URL',
      title: 'Slack アプリ',
    },
    smtp: {
      data_missing: 'データが不足しています',
      description:
        'SMTP プロトコルに基づいてカスタム送信メールを設定します。自動化されたワークフローと組み合わせて、特定のイベントトリガー時にメールを自動送信できます。タスク完了通知、障害アラーム、定期レポートの送信、および大量のマーケティングメールなどのシナリオに適しています。',
      password_label: 'パスワード',
      password_placeholder: 'パスワードを入力してください',
      port_err_msg: 'ポートは数値でなければなりません',
      port_helper_text:
        'SMTPサーバーが使用するポート番号を入力してください。一般的なポートは25、465、および587です。',
      port_input_err_msg: '正しいポートを入力してください',
      port_label: 'ポート',
      port_placeholder: 'SMTP サーバーが使用するポート番号を入力してください',
      server_helper_text:
        '送信メールサーバーアドレス (SMTP) を入力してください。この情報がない場合は、メールサービスプロバイダーにお問い合わせください。',
      server_label: 'SMTP サーバー',
      server_placeholder: 'smtp.example.com',
      title: 'SMTP メールアカウント',
      user_name_label: 'ユーザー名',
      user_name_placeholder: '<EMAIL>',
    },
    telegram: {
      description:
        'Telegram ボットの機能を活用して、グループ、チャンネル、またはプライベートチャットにメッセージを送信します。自動化と組み合わせて、システムの状態更新、イベントリマインダー、チームの動向などのイベントトリガー時に通知を自動的にプッシュし、Telegram プラットフォームでタイムリーな情報提供を実現し、効率的なイベント管理と迅速な対応を促進します。',
      field_bot_token: 'ボットトークン',
      field_bot_token_placeholder: 'ボットトークンを入力してください',
      option_manual_token: 'Bot Tokenを手動入力',
      option_select_bot: '統合からBot Tokenを抽出',
      title: 'Telegram ボット',
      title_token: 'Bot Token',
    },
    tencenthunyuan: {
      description:
        'テンセント混元は、テンセントによって開発された大規模言語モデルであり、強力な中国語の作成能力、複雑な文脈における論理的推論能力、そして信頼できるタスク実行能力を備えています。',
      title: 'テンセント混元 (Tencent Hunyuan)',
      use_cases: [
        'ドキュメントの作成、テキストの校正、テーブルおよびグラフ生成を提供し、作成効率を向上させます',
        '会議でのQ&A、要約、タスクの整理を提供し、会議操作を簡素化し、効率を向上させます',
        '広告素材のインテリジェント作成により、マーケティングコンテンツの作成効率を向上させます',
        'インテリジェントなショッピングガイドを構築し、商業サービスの質と効率を向上させます',
      ],
    },
    third_party_integration: 'サードパーティー連携',
    tongyiqianwen: {
      description:
        '通義千問はアリババクラウドが開発した大規模な言語モデルで、記事、物語、詩などのさまざまな種類のテキストを生成でき、ユーザーのニーズに応じてカスタマイズされた回答やサービスを提供し、問題解決やタスクの完了を支援します。',
      title: '通義千問 (Qwen)',
      use_cases: [
        'データテーブルの記録を集計してレポートを生成',
        'データテーブルの記録に基づいてメール本文を生成',
        'ブログ記事、プレスリリースなどのテキストコンテンツを生成',
      ],
    },
    twitter: {
      create_new_integration: '新しいX(Twitter)アカウントに接続する',
      description:
        'OAuth を介して Twitter アカウントに接続し、自動化されたツイートの作成を実現します。自動化と組み合わせて、ニュースリリース、日次更新、またはマーケティングキャンペーンなどのシナリオでツイートを自動的に投稿し、定期的な情報発信を達成します。これにより、メディアアカウントの活動を維持し、フォロワーとの交流を増やします。',
      form_item_1_label: 'クライアントID',
      form_item_1_placeholder: 'クライアントIDを入力',
      form_item_2_label: 'クライアントシークレット',
      form_item_2_placeholder: 'クライアントシークレットを入力',
      title: 'X(Twitter) OAuth2.0',
    },
    twitter_oauth_1a: {
      access_token_label: 'アクセストークン',
      access_token_placeholder: 'アクセストークンを入力してください',
      access_token_secret_label: 'アクセストークンシークレット',
      access_token_secret_placeholder: 'アクセストークンシークレットを入力してください',
      api_key_helptext:
        'コンシューマーキーはTwitter Developer Platform（https://developer.x.com/en/portal/projects-and-apps）で見つけることができます',
      api_key_label: 'APIキー',
      api_key_placeholder: 'APIキー（コンシューマーキー）を入力してください',
      api_secret_label: 'APIシークレット',
      api_secret_placeholder: 'APIシークレット（コンシューマーシークレット）を入力してください',
      description:
        'OAuth1.0aユーザーコンテキストを介してTwitterアカウントに接続し、メディアファイル（画像、GIF、動画）をアップロードします。自動化と組み合わせることで、メディアコンテンツを含むツイートを投稿できます。',
      title: 'X(Twitter) OAuth1.0a',
    },
    vika: {
      description:
        'Vika は、Vika データベースの接続と管理に使用され、データのクエリ、挿入、更新、および削除をサポートし、ユーザーがデータを効率的に処理および保存できるようにします。',
      title: 'Vika',
      vika_token: 'Vika API Token',
      vika_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    },
    webhook: {
      description:
        'Webhook を使用して外部システムからの HTTP リクエストを受信および処理します。自動化機能と組み合わせることで、特定のイベントを受信した際にデータの更新、通知、またはワークフローの実行などのアクションを自動的にトリガーできます。この統合により、プロセスが簡素化され、外部トリガーへの迅速な対応が保証され、システム全体の効率と接続性が向上します。',
      form_item_1_label: 'Webhook URL',
      form_item_1_placeholder: 'Webhook URL',
      title: 'Webhook',
    },
    wechat: {
      description:
        'BikaアカウントとあなたのWeChatアカウントをリンクさせることで、WeChatのQRコードをスキャンするだけでBikaにログインでき、便利で迅速です。',
      title: 'WeChat Login',
    },
    wecom: {
      description:
        'WeCom のグループボットの Webhook を通じて WeCom グループにメッセージを送信します。自動化と組み合わせて、企業内でリアルタイムのプロジェクト更新、システム通知、または会議のリマインダーに利用できます。WeCom グループでチームメンバーがタイムリーかつ重要な通知を受け取り、チームのコラボレーションと情報伝達の効率を向上させます。',
      form_item_1_label: 'Webhook URL',
      form_item_1_placeholder: 'Webhook URL',
      title: 'WeCom グループボット',
    },
    zapier: {
      description:
        'Zapier は、自動化プラットフォームであり、コードなしまたは低コードのソリューションを通じてアプリケーションとサービスを接続し、ワークフローを簡素化します。Bika のデータベースと自動化機能を統合することで、データがプラットフォーム間でシームレスに流れることができます。',
      title: 'Zapier',
      use_cases: [
        'Zapier を使用してデータを数千のアプリに投稿する',
        'Zapier の自動化から新しいデータベースレコードを作成する',
        '新しいフォーム提出時に Zapier の自動化を有効化する',
        'Zapier の自動化からデータベースレコードを更新する',
        'Zapier の自動化からデータベースレコードを削除する',
      ],
    },
    zoom: {
      description:
        'Zoom 統合により、ユーザーはシステム内で直接 Zoom 会議をスケジュールし、管理できます。自動化フローと組み合わせて、特定のイベントがトリガーされた際に会議の自動作成、招待の送信、参加者のリマインダーを実行できます。オンライン会議やビデオ通話を便利かつ効率的に管理するシナリオに適しています。',
      title: 'Zoom',
      use_cases: [
        '新しいフォーム送信のために Zoom 登録者を作成',
        'イベントトリガー時に自動的に Zoom ミーティングを作成',
        '参加者に Zoom ミーティングのリマインダーを送信',
        '新しい Zoom 録画をデータベースにアップロード',
        'Zoom ミーティングのレポートとサマリーを生成',
      ],
    },
  },
  invite: {
    copy_link: 'リンクをコピー',
    create_invite_description:
      'あなたが作成したリンクを通じてチームに参加したメンバーは、自動的に適切なグループと役割に割り当てられ、両方を獲得します',
    create_invite_loading: '作成中',
    create_public_invitation_link: '公開招待リンクを作成する',
    created_public_invitation_link: '作成された公開招待リンク',
    delete_link: 'リンクを削除',
    email_invite_table: {
      createdAt: '作成日',
      delete: '削除',
      email: 'メール',
      operation: '操作',
      resend: '再送',
      status: 'ステータス',
    },
    input_invite_email_invalid: 'メールアドレスの形式が正しくありません',
    input_invite_member_email: 'メールアドレスを入力してください',
    input_invite_one_or_more_email: '1つまたは複数のメールアドレスを入力し、Enterキーで確認します',
    invite: '招待',
    invite_by_email_desc: 'メールで招待されたメンバーは、両方とも受け取ります',
    invite_by_email_desc_next:
      'は、スペースステーションのアップグレード、プレミアムテンプレートの購入、記念品の引き換えに使用できます。',
    invite_description:
      'このリンクを通じてチームに参加したメンバーは、自動的にそのグループと役割に割り当てられます。成功すると、リンクの作成者と宇宙ステーションに初めて参加したメンバーの両方を取得できます',
    invite_description_next:
      'この通貨は、宇宙ステーションのアップグレード、高級テンプレートの購入、記念品の交換に使用できます。',
    invite_identify: '招待身分',
    invite_identify_guest: 'ゲスト',
    invite_identify_guest_desc:
      'パートナーまたは外部コラボレーターが特定のタスクに参加するためにスペースステーションに招待され、デフォルトでノードリソースを表示できません',
    invite_identify_member: 'メンバー',
    invite_identify_member_desc:
      '組織またはチームの内部コラボレーターが、プロジェクト作業に共同で参加します',
    invite_link_created_by: 'が作成した招待リンク',
    invite_link_created_fail: '作成失敗',
    invite_link_created_success: '作成成功',
    invite_link_have_coins: '現在、を持っています',
    invite_members: 'メンバーを招待',
    invite_outsider_invite_input_already_exist: 'このメールアドレスは既に招待されています',
    invite_people: '人を招待',
    invite_people_by_email: 'メールで招待する',
    invite_people_by_email_button: '招待する',
    invite_people_by_email_description: 'メールアドレスをカンマ区切りで入力します',
    invite_people_by_email_placeholder: 'メールアドレス',
    invite_people_by_link: 'リンクで招待する',
    invite_people_by_link_button: 'コピー',
    invite_people_by_link_copied: 'リンクをコピーしました',
    invite_people_by_link_description: 'リンクを共有してチームに人を招待します',
    invite_people_description: 'チームに人を招待します',
    invite_record: '招待履歴',
    invite_record_description:
      '招待された方が参加すると、招待者と招待された方の両方が報酬を受け取ることができます。これはスペースステーションのアップグレード、プレミアムテンプレートの購入、記念品の引き換えに使用できます。',
    invite_role: '役割（任意）',
    invite_role_placeholder: '役割を選択してください',
    invite_status: {
      accepted: '受け入れ済み',
      pending: '保留中',
      rejected: '拒否されました',
    },
    invite_team: 'チーム（必須）',
    invite_team_placeholder: 'グループを選択してください',
  },
  launcher: {
    ai: 'AI',
    ai_launcher: 'AIランチャー',
    ai_launcher_description:
      'AI ランチャーは、ユーザーがさまざまな操作を効率的に実行できるように設計されたスマートで迅速な機能です。シンプルで直感的なインターフェースを通じて、コマンドの実行、ファイルの検索、スペースステーションの設定などが簡単に行えます。日常のタスクから複雑な操作まで、AI ランチャーは迅速かつ効率的なソリューションを提供し、作業効率を大幅に向上させます。',
    ask_me_anything: '何でも質問してください...',
    chat_history: 'チャット履歴',
    chat_replay: 'チャット返信',
    command: 'コマンド',
    command_not_found: '結果が見つかりません。AIに質問してください。',
    commands: 'コマンド ',
    commands_placeholder: '実行したいコマンドは何ですか...',
    database_record: 'データベース・レコード',
    database_record_placeholder: 'データベース・レコードを検索...',
    document: 'ドキュメント',
    document_placeholder: 'ドキュメントを検索...',
    file: 'ファイル',
    file_placeholder: '名前でファイルやフォルダを検索...',
    getting_started: '初心者向け',
    help: 'ヘルプ',
    help_placeholder: 'ヘルプを検索...',
    launcher: 'ランチャー',
    mission: 'ミッション',
    node: 'ノードリソース',
    pending_todos: '進行中のミッション',
    recently: 'さいきん',
    record: '記録',
    reminder: 'リマインダー',
    router: 'ルーター',
    search_tip: 'あいまい検索をサポートする',
    shortcuts: 'ショートカット',
    smart: 'スマート',
    task: 'タスク',
    templates: 'テンプレート',
    ui_modal: 'UIモーダル',
    unread_reports: '未読のレポート',
    url: 'URL',
  },
  license: {
    apply_description: 'Bika.ai セルフホストインストールパッケージを入手する。',
    apply_title: 'Bika.ai セルフホストライセンス申請',
    company_name: '貴社/チーム名は何ですか？',
    company_size: '貴社/チームの人数は何人ですか？',
    copy_your_license_key: 'ライセンスキーをここにコピーしてください',
    create_self_host_license_key: 'プライベートデプロイメント向けライセンスキーの作成(beta)',
    download_and_install_self_host: 'ダウンロードしてプライベートホストをインストールする',
    get_self_host_license_key: 'プライベートホスト展開のライセンスキー（License Key）を取得する',
    get_trial: 'あなたは一回 30 日間の試用の機会を得ることができます',
    industry: '貴社の業界は何ですか？',
    license_key: 'ライセンスキー',
    license_key_expired: 'ライセンスキーが期限切れです',
    license_key_expired_desc: 'トライアル期間が終了しました',
    please: 'お願いします',
    submit: 'Bika.ai セルフホストパッケージをインストールする',
    trial_desc: '現在、あなたは30日の試用期間中です。使用中に問題が発生した場合',
  },
  mission: {
    after_action: 'ミッション完了後の次のアクション',
    allow_complete_manually: '手動完了を許可',
    allow_reject: '拒否を許可',
    assign_type: '割り当てタイプ',
    assign_type_dedicated: '専用タスク',
    assign_type_dedicated_description:
      'タスクは複数のメンバーに送信されますが、各メンバーが個別に完了する必要があります',
    assign_type_shared: '共有タスク',
    assign_type_shared_description:
      'タスクは複数のメンバーに送信され、いずれかのメンバーが完了すると完了と見なされます',
    assigned: '担当者',
    assignee: '割り当てられた人 ',
    button_text: 'ボタンテキスト',
    can_transfer: '転送を許可',
    complete: '完了をマーク',
    completedAt: '完了時間',
    confirm: '同意する',
    createAt: '発起時間',
    create_record: 'レコードの作成',
    create_success: 'タスクの作成に成功しました',
    database_record: 'データベース・レコード',
    database_view: 'データベース・ビュー',
    date_time_now: '今',
    date_time_today: '今日',
    description: 'タスクの内容',
    description_placeholder: 'タスクの内容を入力してください',
    detail: '詳細を見る',
    dueAt: '期限',
    dynamic_datetime: '動的日時',
    dynamic_datetime_type: '動的日時タイプ',
    end_time: 'タスクの開始時間と終了時間を表示',
    features_list: 'Missions List',
    force_popup: '強制ポップアップ',
    go_to_mission: 'ミッションに行く',
    initiator: '発起人',
    know_and_next: '了解しました、次へ進む',
    mission: 'ミッション',
    mission_description:
      'Mission is a smart, automative, traceable tasks differs from typical tasks or to-do lists, which you have to check off by yourself.\nFor example, consider the Create Record Mission: when a user receives it, the mission will automatically be marked as complete only when the required record has been created.',
    mission_name: 'ミッション名',
    mission_name_placeholder: 'ミッション名を入力してください',
    mission_type: 'ミッションタイプ',
    modal_content_mission_invalid:
      'そのタスクに関連するリソースにアクセスできなくなりました。タスクを削除しますか',
    modal_title_mission_invalid: '温かいヒント',
    more_setting: '詳細設定',
    msg_mission_completed: 'タスクが完了しました！',
    msg_mission_rejected: 'ミッションが拒否されました。',
    msg_no_next_action: '次のアクションはありません。',
    msg_transfer_not_supported: '転送はサポートされていません。',
    next: '次へ',
    placeholder_assign_type: '割り当てタイプを選択してください',
    processing: '処理中',
    progress: '進捗',
    reject: 'タスクを拒否',
    reminder: 'タスクのリマインダー時間',
    reminder_description: 'リマインダーの説明',
    reminder_title: 'リマインダーのタイトル',
    reminder_to: 'リマインド対象',
    repeat: 'リピートリマインダーを設定',
    seconds: '({seconds}秒)',
    show_end_time: '終了時間を設定',
    show_start_end_time: 'タスクの開始時間と終了時間を設定',
    show_start_time: '開始時間を設定',
    specific_datetime: '特定日時',
    start_time: '開始時間',
    taskIntroduction: 'タスク紹介',
    taskObjective: 'タスク目標',
    taskStatus: 'タスクの状態',
    time_internal: '時間間隔',
    tips_for_readme_mission:
      'このドキュメントを新しいタブで開くと、見ながら実践するのに役立ちます。',
    transfer: 'タスクを転送',
    type: {
      ai_create_records: {
        description: 'AI によって生成されたデータを使用して、レコードを作成する',
        name: 'AI によるレコードの作成',
      },
      approval: {
        description:
          '一つのステップのみを含むタスクで、タスクの担当者は承認、拒否、または他人に移譲することができる。例えば、休暇申請の承認',
        name: '承認タスク',
      },
      comment_record: {
        description: 'そのタスクを受け取ったメンバーは、指定されたレコードにコメントする必要がある',
        name: 'レコードへのコメント',
      },
      create_multi_records: {
        description:
          'そのタスクを受け取ったメンバーは、指定された数のデータベースレコードを作成する必要がある',
        name: '複数のレコードの作成',
      },
      create_record: {
        description:
          'そのタスクを受け取ったメンバーは、指定されたデータベースのレコードを作成する必要がある',
        name: 'レコードの作成',
      },
      create_task: {
        description: 'このタスクを受け取ったメンバーは、新しいタスクを作成する必要があります',
        name: 'タスクの作成',
      },
      enter_view: {
        description:
          'そのタスクを受け取ったメンバーは、データベースの特定のビューを閲覧するように案内される',
        name: '特定のビューの閲覧',
      },
      google_meet: {
        description: 'Google 会議のリンクを含むタスクで、オンライン会議の招待に適している',
        name: 'Google 会議',
      },
      install_template: {
        description:
          'そのタスクを受け取ったメンバーは、テンプレートのインストールを完了する必要がある',
        name: 'テンプレートのインストール',
      },
      invite_member: {
        description: 'そのタスクを受け取ったメンバーは、メンバーの招待操作を一度完了する必要がある',
        name: 'メンバーの招待',
      },
      quest: {
        description:
          '複数の関連するサブタスクで構成されるタスクセット。例えば、初心者向けのタスクシナリオ',
        name: 'シリーズタスク',
      },
      read_markdown: {
        description:
          'そのタスクを受け取ったメンバーは、指定された Markdown ドキュメントを閲覧する必要がある',
        name: 'Markdown ドキュメントの閲覧',
      },
      read_template_readme: {
        description:
          'そのタスクを受け取ったメンバーは、指定されたテンプレートの説明文書を閲覧する必要がある',
        name: 'テンプレート説明の読み取り',
      },
      redirect_space_node: {
        description:
          'そのタスクを受け取ったメンバーは、指定されたスペースノードにリダイレクトされる',
        name: 'スペースノードへのリダイレクト',
      },
      reminder: {
        description: 'このタスクを受け取ったメンバーは、リマインドメッセージを受け取ります',
        name: 'リマインダー',
      },
      review_record: {
        description:
          'そのタスクを受け取ったメンバーは、データベースの特定のレコードを閲覧するように案内される',
        name: '特定のレコードの閲覧',
      },
      sequence: {
        description:
          '複数のステップを含み、順番に完了する必要があるタスク。例えば、複数レベルの承認ワークフロー',
        name: 'シーケンスタスク',
      },
      set_space_name: {
        description:
          'そのタスクを受け取ったメンバーは、スペース設定画面に案内され、スペース名の設定を完了する必要がある',
        name: 'スペース名の設定',
      },
      submit_form: {
        description: 'タスクを作成し、特定のメンバーに割り当て、指定されたフォームの記入を依頼する',
        name: 'フォームの提出',
      },
      submit_multiple_form: {
        description:
          'タスクを受け取ったメンバーは、指定された複数のフォームを全て記入し終える必要がある。例えば、契約書の入力では、顧客、契約、支払い記録の3つのフォームを記入する必要がある',
        name: '複数のフォームの提出',
      },
      ui_launcher: {
        description:
          'ユーザーインターフェース関連の機能やアプリケーションを起動するために使用されます',
        name: 'UI ランチャー',
      },
      update_record: {
        description:
          'そのタスクを受け取ったメンバーは、指定されたレコードの編集を完了する必要がある',
        name: 'レコードの更新',
      },
      voov_meet: {
        description: 'Voov 会議のリンクを含むタスクで、オンライン会議の招待に適している',
        name: 'Voov 会議',
      },
      zoom_meet: {
        description: 'Zoom 会議のリンクを含むタスクで、オンライン会議の招待に適している',
        name: 'Zoom 会議',
      },
    },
    update_record: 'レコードの更新',
  },
  navbar: {
    agent_builder: 'エージェントビルダー',
    agent_builder_description:
      '私はあなたのエージェントビルダーです。人を雇（やと）いません。ただエージェントを構築（こうちく）します',
    beta: 'ベータ',
    chief_of_staff: 'チーフオブスタッフ',
    chief_of_staff_description:
      '私はあなたのチーフオブスタッフです。情報検索（じょうほう けんさく）やコンテンツ生成など、さまざまなタスクをサポートいたします',
    expert: '専門家',
    explore: '探索',
    home: 'ホーム',
    personal: '個人',
    personal_resources: '個人的な資源',
    private: 'プライベートリソース',
    private_description:
      'プライベートリソース：あなただけの専用リソーススペースで、自由に編集、追加、削除が可能です',
    report: 'レポート',
    resources: 'ノードリソース',
    shortcuts: 'ショートカット',
    shortcuts_description:
      'ショートカット：管理者または自分で設定したショートカットが含まれており、よく使うリソースへのアクセスが速くなります',
    shortcuts_resources: 'ショートカット資源',
    smart: 'スマート',
    smart_description:
      'スマート：グローバル検索、スマートタスク、スマートレポートをサポートし、ゴミ箱とテンプレートセンターが装備されています',
    space_launcher: 'スペースランチャー',
    space_launcher_description: 'スペースのためのスペースランチャー',
    super_agent: 'スーパーエージェント',
    team: 'チーム',
    team_resources: 'チームリソース',
    team_resources_description:
      'チームリソース：チームメンバーと共同で編集、追加、削除を行い、チームの協力とリソースの共有を容易にします',
    todo: 'タスク',
  },
  node: {
    delete_node: 'ノードを削除',
    delete_node_description: 'ノード「{name}」を削除しますか？',
    delete_node_success: 'ノード「{name}」の削除に成功しました',
    edit_folder: 'フォルダーを編集',
    empty_folder: '空のフォルダー',
    export_attachments: '添付ファイルのエクスポート',
    export_bika_file: '.bika ファイルをエクスポート',
    export_excel: 'Excelファイルにエクスポートする',
    export_template: '.bika テンプレートをエクスポート (内部)',
    import_bika_file: '.bika ファイルからインポート',
    jump_to_node: 'ノードに移動',
    node: 'ノード',
    node_detach: 'テンプレートから切り離し',
    node_detach_fail: 'テンプレートからの切り離しに失敗しました',
    node_detach_success: 'テンプレートからの切り離しに成功しました',
    node_guide: {
      automation: {
        description:
          'Automation allows you to set triggers and actions to automate workflow processes.',
        feature1: '• Multiple trigger conditions',
        feature2: '• Rich action types',
        feature3: '• Conditional branching logic',
        feature4: '• Execution history',
        tip1: '💡 You can set multiple trigger conditions',
        tip2: '💡 Test automation rules before enabling them',
        title: 'Welcome to Automation',
      },
      dashboard: {
        description:
          'Dashboards help you visualize data, create charts and reports to gain insights into data trends.',
        feature1: '• Multiple chart types',
        feature2: '• Real-time data updates',
        feature3: '• Custom layouts',
        feature4: '• Data filters',
        tip1: '💡 You can add multiple data sources',
        tip2: '💡 Support exporting charts and data',
        title: 'Welcome to Dashboard',
      },
      database: {
        description:
          'Database is the core feature of Bika, allowing you to store and manage data in a structured way.',
        feature1: '• Create custom field types',
        feature2: '• Multiple views to display data',
        feature3: '• Powerful filtering and sorting',
        feature4: '• Collaboration and permission management',
        tip1: '💡 You can drag and drop to reorder fields',
        tip2: '💡 Use views to create different data display modes',
        title: 'Welcome to Database',
      },
      default: {
        description: 'This is a powerful feature module that makes your work more efficient.',
        feature1: '• Intuitive and easy-to-use interface',
        feature2: '• Powerful feature set',
        feature3: '• Flexible configuration options',
        tip1: '💡 Explore various features to discover more possibilities',
        title: 'Welcome to this Feature',
      },
      form: {
        description:
          'Forms allow you to easily collect and organize information, with data automatically syncing to the associated database.',
        feature1: '• Drag-and-drop form designer',
        feature2: '• Multiple field type support',
        feature3: '• Automatic data validation',
        feature4: '• Conditional logic support',
        tip1: '💡 You can set display conditions for fields',
        tip2: '💡 Form submissions sync to the database in real-time',
        title: 'Welcome to Form',
      },
      got_it: 'Got it',
      main_features: 'Main Features:',
      tips: 'Tips:',
    },
    node_info: 'ファイル情報',
    node_name: 'Node Name',
    node_update: 'テンプレートを更新',
    permission: {
      add_permission_message:
        '権限を設定する必要があるメンバー、グループ、または役割を追加してください。',
      can_comment: 'コメント可能',
      can_edit: '編集可能',
      can_edit_content: '更新のみ',
      can_edit_content_desc:
        '既存のコンテンツを更新できますが、データベース内のレコードを追加または削除することはできません。',
      can_edit_desc: 'コンテンツを編集でき、データベース内のレコードを追加または削除できます。',
      can_view: '閲覧可能',
      can_view_desc: '編集できません。リソースの内容を閲覧することのみ可能です。',
      description:
        '異なる役割、メンバー、および部門に基づいて権限を細かく割り当てることで、データアクセスおよび操作権限を効果的に制御できます。権限管理を通じて、各チームメンバーが自分の責任に関連する機能およびデータにのみアクセスおよび操作できるようにすることで、データのセキュリティとプライバシーを大幅に向上させることができます。',
      full_access: '管理可能',
      full_access_desc: 'リソースに対するすべての操作権限を持っています。',
      login_to_edit: '編集するにはログイン',
      no_access: 'アクセス不可',
      no_access_desc: 'リソースを閲覧する権限がありません。',
      permission_description: {
        CAN_COMMENT: 'このリソースを閲覧し、コメントできます',
        CAN_EDIT: 'コンテンツを編集し、データベース内のレコードを管理できます',
        CAN_EDIT_CONTENT: '既存のコンテンツを更新できますが、レコードの追加や削除はできません',
        CAN_VIEW: 'このリソースの内容を閲覧することのみ可能です',
        FULL_ACCESS: 'このリソースに対するすべての操作権限があります',
        NO_ACCESS: 'このリソースにアクセスする権限がありません',
      },
      remove: '権限を削除',
      title: '権限管理',
    },
    publish_to_template_center: 'テンプレートセンターに公開',
    republish: '再度公開',
    view_original_template: '元のテンプレートを表示',
  },
  notification: {
    all: 'すべて',
    all_notifications_marked_as_read: 'すべての通知が既読となりました',
    app_notification: 'アプリ通知',
    app_push_notification: 'アプリプッシュ通知',
    browser_notification: 'ブラウザ通知',
    clean_all_notifications: 'すべての通知をクリア',
    confirm_to_clean_all_notifications: 'すべての通知をクリアしますか?',
    mail_notification: 'メール通知',
    mark_all_as_read: 'すべて既読にする',
    new_agenda: '新しい予定',
    new_mission: '新しいミッション',
    new_report: '新しいレポート',
    no_notification_so_far: '通知はありません',
    notification: '通知',
    notification_settings: '通知設定',
    notification_type: '通知の種類',
    sms_notification: 'SMS通知',
    system_message: 'システムメッセージ',
    unread: '未読',
  },
  ok: 'OK',
  pagination: {
    load_more: 'もっと読む',
    loading: '読み込み中...',
    no_more: 'もうありません',
  },
  pricing: {
    business: 'ビジネス',
    change_your_plan: 'プランを変更',
    community: 'コミュニティ',
    currency_symbol: '¥',
    customize: 'カスタマイズ',
    customize_seat: 'カスタムシート',
    enterprise: 'エンタープライズ',
    enterprise_private_cloud: 'エンタープライズ（プライベートクラウド）',
    enterprise_self_hosted: 'エンタープライズ（セルフホスト）',
    experience_now: '今すぐ体験',
    features: {
      advanced_automation_integrations: '高度な自動化統合',
      advanced_automation_integrations_tips:
        '高度な自動化トリガーとコネクタを使用して、高度なAIモデルなどの外部ツールを接続します',
      api_calls_per_month: '月ごとのAPIコール数',
      api_rate_limits: 'APIのレート制限',
      authorized_email_domain: '認証されたメールドメイン',
      automation_integrations: '自動化統合',
      automation_integrations_tips: '外部ツールを接続するための自動化トリガーとアクチュエーター',
      automation_run_history: '自動化実行履歴',
      automation_runs_per_month: '月ごとの自動化実行数',
      browser_notifications: 'ブラウザ通知',
      byok_support: 'BYOK対応',
      coming_soon: '近日公開',
      community: 'コミュニティ',
      credits_per_seat_per_month: 'メンバーごとに毎月AIクレジットを付与',
      credits_per_seat_per_month_tips:
        'クレジットはスペースに付与され、「メンバー数 × 月間クレジット」で計算され、毎月リセットされます。クレジットはAIモデルの利用に使用されます。',
      custom_domain: 'カスタムドメイン',
      data_sync: 'データ同期',
      email_support: 'メールサポート',
      export_bika_file: '.bika ファイルをエクスポート',
      export_bika_file_tips:
        '.bika は、関連、自動化などの完全なデータと構造を Bika にインポートできるファイル形式です',
      export_excel_csv: 'Excel/CSV ファイルをエクスポート',
      help_center: 'ヘルプセンター',
      im_support: 'IMサポート',
      im_support_tips: 'WhatsAppを通じてサポートを受けることができます',
      integration_instances: '統合インスタンス',
      managed_emails_per_month: 'Bikaメールサービス(毎月)',
      managed_emails_per_month_tips:
        'Bika公式メールサービス、自動化プロセスでbikaドメインを使用してメールを送信します',
      max_guest: '最大ゲスト数',
      max_records_per_database: 'データベースあたりの記録数',
      max_records_per_space: 'スペースあたりの記録数',
      max_seat: '最大座席数',
      mirror_sync: 'ミラー同期',
      missions_per_month: '月ごとのミッション数',
      mobile_notifications: 'モバイル通知',
      notification_sms: '通知SMS',
      planned_feature: '計画中の機能',
      private_template: 'プライベートテンプレート',
      professional_services: 'プロフェッショナルサービス',
      publish_and_share: '公開と共有',
      publish_template: 'テンプレートを公開',
      remove_logos: 'ロゴを削除',
      remove_logos_tips: 'インターフェースに Bika のロゴは表示されません',
      reports_per_month: '月ごとのレポート数',
      resource_nodes: 'リソースノード',
      resource_permissions: 'リソース権限',
      self_hosted: 'セルフホスティング',
      self_hosted_tips:
        '自分のサーバーに Bika をデプロイし、インストールインスタンスをホワイトラベル化（つまり、ブランドロゴを削除してブランドカスタマイズ）することもできます',
      sell_template: 'テンプレートを販売',
      sms_notifications: 'SMS通知',
      smtp_emails_per_month: '月ごとのSMTPメール数',
      space_audit_log: 'スペース監査ログ',
      space_sessions_log: 'スペースセッションログ',
      storage: 'ストレージ',
      storage_tips:
        '単一のワークスペース内のすべてのデータベースとドキュメントに保存される添付ファイルの総ストレージ制限。',
      sub_admin: 'サブ管理者',
      sub_domain: 'サブドメイン',
      unlimited: '無制限',
      user_sessions_log: 'ユーザーセッションログ',
      webinar: 'ウェビナー',
    },
    for_businesses_and_enterprises: '企業向け',
    for_individual_and_teams: '個人およびチームのため',
    free: '無料',
    free_number: '¥0',
    free_trial_7_days: '7日間無料、その後は毎年{price}',
    includes_word: '含む',
    modal_title: 'プランを変更',
    month: '月',
    monthly: '月次',
    oncely: {
      oncely_code: 'Oncely 引換コード',
      oncely_code_management: '交換コード管理',
      oncely_code_placeholder: 'Oncely 引換コードを入力してください',
    },
    page_section_detail_title: 'プランと機能を比較',
    page_section_question_title: '製品価格Q&A',
    payment_successful: 'Congratulations on successfully upgrading to {plan}',
    payment_successful_description:
      'You have successfully upgraded to {plan}. You have received the following benefits:',
    per_seat: '/ 席',
    plus: 'プラス',
    popular: '最も人気',
    price: '価格',
    pro: 'プロ',
    question: {
      answer_1: '回答1',
      question_1: '質問1',
    },
    renew_and_cancel: 'プランはキャンセルするまで毎年自動更新されます',
    seat: '座席数',
    subscription_cycle: 'メンバー一人当たり/毎月',
    team: 'チーム',
    user: 'ユーザー',
    view_benefit_details: '特典の詳細を見る',
    view_detail: '詳細を見る',
    year: '年',
    yearly: '年次',
  },
  publish_template: {
    allow: '許可',
    allow_detach_description:
      '許可すると、テンプレートをインストールしたユーザーはそれを分離できます。分離後、元のテンプレートからアップグレードすることはできませんが、二次公開に使用できます。',
    allow_users_to_detach_template: '他のユーザーがテンプレートを分離することを許可',
    author_space_title: '空間名を表示',
    author_user_title: '私のニックネームを見せて',
    cancel: 'キャンセル',
    category: 'カテゴリー',
    coming_soon: '近日公開予定のテンプレートアプリケーション',
    coming_soon_description:
      'すべてのユーザーがテンプレートアプリケーションであなたのテンプレートを検索できますが、インストールすることはできません',
    configure_template: 'テンプレートを設定',
    forbid: '禁止',
    init_mission: '初期化ミッション',
    init_mission_description:
      'このテンプレートに初期化ミッションを設定できます。ユーザーがテンプレートをインストールしたときに、初期化ミッションが自動的に割り当てられます。',
    keywords: 'キーワード',
    private: 'プライベート',
    private_description:
      'プライベート：ステーションメンバーのみが見れます。パブリック：全てのbikaユーザーが見れます。',
    public: 'パブリック',
    public_description:
      'すべてのユーザーが「公開テンプレートアプリケーション」で、あなたのテンプレートを検索してインストールできます',
    publish_data: '公開テンプレートアプリケーション',
    publish_data_description:
      'データを公開すると、他のユーザーがこのテンプレートをインストールするときにデータも一緒にインストールされます。',
    publish_success: '公開成功',
    publish_template: 'テンプレートを公開',
    publish_to_template_center: 'テンプレートセンターに公開',
    space: 'ステーション内のテンプレートアプリケーション',
    space_description:
      'このスペースステーションのメンバーのみが、「あなたのスペースステーションのテンプレートアプリケーション」でご自身のテンプレートを検索してインストールできます',
    template_author: '著者',
    template_id: 'テンプレートID',
    template_published: 'テンプレートが公開されました',
    template_published_description_coming_soon:
      'すべてのユーザーがテンプレートアプリケーションであなたのテンプレートを検索できますが、インストールすることはできません',
    template_published_description_public:
      'すべてのユーザーが「公開テンプレートアプリケーション」で、あなたのテンプレートを検索してインストールできます',
    template_published_description_space:
      'このスペースステーションのメンバーのみが、「あなたのスペースステーションのテンプレートアプリケーション」でご自身のテンプレートを検索してインストールできます',
    template_visibility: 'テンプレートの公開',
    use_cases: '使用ケース',
    version_already_exist: 'バージョンが既に存在します',
    version_description: 'バージョン説明',
    version_number: 'バージョン番号',
    view_template_center: 'テンプレートセンターを表示',
  },
  record: {
    active: 'ダイナミック',
    activity: {
      anonymous: '匿名者',
      comment_tip: 'Shift+Enterで改行、Enterで送信',
      empty_activity: 'まだアクティビティがありません',
      empty_comment: 'コメントなし',
      just_changelog: '変更履歴のみ',
      just_comment: 'コメントのみ',
      load_more: 'もっと見る',
      loading: '読み込み中...',
      no_more: 'もっと見る',
      record_comment_and_change: 'レコードのコメントと変更履歴',
    },
    add_attachment: '添付ファイルを追加',
    add_local_file: 'ローカルファイルを追加する',
    create: '作成',
    create_record: 'レコードを作成',
    create_record_button: 'レコードを作成',
    create_record_description: '新しいレコードを作成します',
    create_record_failed: 'レコードの作成に失敗しました',
    create_record_success: 'レコードの作成に成功しました',
    delete_comment: 'コメントを削除',
    delete_comment_description: '本当にこのコメントを削除しますか？',
    delete_record: 'レコードを削除',
    drop_file_upload: 'ファイルをここにドロップしてアップロードする',
    empty_comment: 'コメントがありません',
    first_record: '最初のレコード',
    go_next: '次のページに進む',
    go_previous: '前のページに戻る',
    input_comment_placeholder: 'コメントを入力してください、@誰かをメンション',
    max_file_size: '最大ファイル体格: 500 MB',
    modify_record: 'レコードを変更',
    no_next_record: '次のレコードはありません',
    no_previous_record: '前のレコードはありません',
    paste_or_drop_file_upload:
      '貼り付けるかファイルをここにドラッグアンドドロップしてアップロードする',
    record: 'レコード',
    record_comment: 'レコードコメント',
    record_delete: 'レコード削除',
    record_detail: 'レコード詳細',
    record_detail_description:
      'Bika.aiでは、ユーザーが各記録をクリックして展開できます。記録詳細は、特定の記録に関連するすべての詳細情報を含む展開ビューです。',
    record_pin: 'レコードピン',
    record_unnamed: '名前のないレコード',
    request_modify: '修正リクエスト',
    request_new_record: '新しいレコードの作成をリクエスト',
    select_date: '日付を選択',
    select_from_files: 'ファイルから選択',
    select_from_gallery: 'ギャラリーから選択',
    select_member: 'メンバーを選択',
    select_option: 'オプションを選択',
    tab_general: '基本情報',
    take_photo_or_record_video: '写真を撮るまたはビデオを録画',
  },
  redeem: {
    oncely: {
      congratulations: 'おめでとうございます！',
      contact_service: 'カスタマーサービスに連絡',
      email: '電子メール',
      email_code: '確認コード',
      enter_space: '宇宙ステーションに入る',
      input_email: 'メールアドレスを入力してください',
      input_email_code: '確認コードを入力してください',
      input_oncely_code: '交換コードを入力してください',
      logout: 'ログアウト',
      new_user_tip:
        'ご注意ください。交換コードを使用することで、支払い権利を含む新しい宇宙ステーションが得られます。古い宇宙ステーションは交換コードのアップグレードをサポートしていません！',
      new_user_tip_ignore_code:
        'アクティベーションに成功すると、新しいスペースが作成され、すべての階層固有の機能にフルアクセスできるようになります。既存のスペースはこの方法でアップグレードできないことにご注意ください。',
      old_user_tip:
        '以前に登録された{spaceCount}個のスペースが検出されました。認証が成功すると、すべてのティアの機能に完全にアクセスできる新しいスペースが作成されます。既存のスペースはこの方法ではアップグレードできませんのでご注意ください。',
      oncely_code: '交換コード',
      question: '問題に遭遇',
      reedem_oncely: '取引を交換する',
      submit: '送信',
      success_redeem: '✨ 新しいスペースが作成され、すべてのプレミアム機能が利用可能になりました！',
      you_have_used_fragment_1: '現在ログインしているユーザー：',
      you_have_used_fragment_2:
        '。下の送信ボタンをクリックすると、新しいスペースが即座に作成され、サブスクリプションの階層に対応するすべての機能が有効になります。',
    },
  },
  referral: {
    bika_coins_description: 'ポイントはさまざまなサービスや製品と交換できます',
    check_usage: '使用状況を確認する',
    current_space_plan: '現在のスペースプラン',
    earn_bika_coins: 'ポイントを獲得する',
    method_1: '方法1：招待リンクで招待する',
    method_2: '方法2：招待コードで招待する',
    method_3: '方法3：スペースに招待する',
    method_4: '方法4：モバイルアプリをインストールする',
    other_referral_code: '招待コード',
    referral: '紹介',
    referral_code: '紹介コード',
    referral_rewards: '他人の招待コードを入力すると、両方が報酬を得ることができます',
    reward_history: '報酬履歴',
    total: '合計：',
    view_my_referral_code: '私の招待コードを見る',
    your_bika_coins: 'あなたのBikaコイン',
  },
  reminder: {
    no_reminder_so_far: 'リマインダーはありません',
    remind: 'リマインド',
    remind_me: '私にリマインドして',
    reminder: 'リマインダー',
    reminders: 'リマインダー',
  },
  report: {
    create_report: 'レポートを送信',
    create_report_description:
      '自動化プロセスを通じてレポートの生成をトリガーすることができます。レポートの内容はMarkdownなどの形式で指定された個人やグループに送信されます。このレポートは、AIが設定に基づいて自動的に生成したデータと情報を提供し、プロジェクトの進捗をよりよく理解できるようにサポートします。',
    mark_all_as_read: 'すべて既読にする',
    mark_all_as_read_content: 'すべての未読レポートを一度のクリックで既読にするか確認してください',
    mark_as_read: '既読にする',
    no_report_so_far: 'これまでのレポートはありません',
    read: '既読',
    read_report: '既読レポート',
    report: 'レポート',
    report_description:
      'AIや自動化によって事前定義されたルールやデータに基づいて生成されます。レポートは、メール、記事、または文書の形式で提供されることがあります。',
    report_detail: '詳細を情報する',
    report_detail_description:
      'レポートの詳細情報を表示します。自動化プロセスが終了するたびに、AIがユーザー設定に基づいて自動的にレポートを生成し、ユーザーが作業の進捗をよりよく振り返るのを支援します。',
    report_info: 'レポート情報',
    reports: 'レポート',
    unread: '未読',
  },
  resource: {
    add_filter_condition: 'フィルター条件の追加',
    add_shortcut_success: 'ショートカットが正常に追加されました',
    ai_page: {
      settings_html_copilot: 'AI アシスタント',
      settings_html_description:
        '下記にHTMLページコードを入力し、保存をクリックすると、ページが自動的に生成されます',
      settings_html_placeholder: 'HTMLページを入力してください',
      settings_html_title: 'HTMLページ',
      welcome: '右側の AI アシスタントにアイデアを伝えると、ページを作成してくれます',
    },
    all_resources: 'すべてのリソース',
    automation_name: '自動化名',
    can_not_create_integration: '連携を作成できません。管理者に連絡してください。',
    cancel_excel_import: 'インポートのキャンセル',
    cancel_excel_import_description: 'インポートをキャンセルしますか？',
    cancel_incremental_import: '增量インポートのキャンセル',
    cancel_incremental_import_description: '增量インポートをキャンセルしますか？',
    change_cover: 'カバーを変更',
    change_form_logo: 'ロゴを変更',
    close_export_modal_warning: 'エクスポート中です。閉じますか？',
    content_changed_warning:
      'データテーブルの内容が更新されました。最新の内容を表示するには、更新してください',
    content_is_empty: 'からになる',
    create_ai_agent_success: 'AI エージェント "{name}" の作成に成功しました',
    create_ai_page_success: 'AI ページ "{name}" の作成に成功しました',
    create_automation_action_success: 'アクション "{name}" を正常に作成しました',
    create_automation_success: '自動化 "{name}" を正常に作成しました',
    create_dashboard_success: 'ダッシュボード "{name}" を正常に作成しました',
    create_database_success: 'データベース "{name}" を正常に作成しました',
    create_document_success: 'ドキュメント "{name}" を正常に作成しました',
    create_folder_success: 'フォルダー "{name}" を正常に作成しました',
    create_form_success: 'フォーム "{name}" を正常に作成しました',
    create_from_blank: '空白から作成',
    create_from_blank_automation_description: '自動化プロセスを一から構築',
    create_from_blank_dashboard_description: 'データを可視化する新しいダッシュボードを作成',
    create_from_blank_database_description: 'データを保存する新しいデータベースを作成',
    create_from_blank_document_description: 'コンテンツを書く新しい文書を作成',
    create_from_blank_folder_description: 'リソースを整理する新しいフォルダを作成',
    create_from_template:
      '下記からシーンに合ったテンプレートを選択して、素早く開始できます。適切なテンプレートがない場合は、「空白のリソースを作成」をクリックしてカスタム構築できます。',
    create_integration: '連携を作成する',
    create_mirror_success: '鏡 "{name}" を正常に作成しました',
    create_view: 'ビューを作成する',
    create_view_success: 'ビュー "{name}" を正常に作成しました',
    dashboard_description: 'ダッシュボード説明',
    dashboard_name: 'ダッシュボード名',
    data_is_fetching: 'データを取得しています',
    day: '日',
    delete_field: 'フィールドを削除',
    delete_field_description: '削除すると元に戻せません。フィールドを削除しますか、',
    delete_folder_error: 'フォルダー "{name}" の削除に失敗しました',
    delete_folder_success: 'フォルダー "{name}" を正常に削除しました',
    delete_resource_description: '{name}を削除しますか？',
    delete_resource_error: 'リソース "{name}" の削除に失敗しました',
    delete_resource_success: 'リソース "{name}" を正常に削除しました',
    delete_view: 'ビューを削除',
    delete_view_description: 'ビューを削除してもよろしいですか：{name}？',
    delete_view_error: 'ビュー "{name}" の削除に失敗しました',
    delete_view_success: 'ビュー "{name}" を正常に削除しました',
    description: 'ノードリソースは、データベース、自動化、フォームなどのノード実装の一種です。',
    download_again_file: 'ダウンロードに失敗しましたか？ここをクリックして再試行してください',
    download_done_file:
      '解析が完了し、ダウンロードが始まりました。ブラウザのダウンロード履歴で確認できます',
    download_loading_file:
      'リクエストを処理しています。数分かかることがあります。操作をキャンセルしないように、ページを更新したり戻ったりしないでください',
    download_template: 'テンプレートをダウンロード.xlsx',
    edit_automation: '自動化フローを編集',
    edit_automation_action: '自動化の実行アクションを設定',
    edit_automation_trigger: '自動化のトリガールールを設定',
    edit_dashboard: 'ダッシュボードを編集',
    edit_database: 'データベースを編集',
    edit_database_view: 'ビューを編集',
    edit_field: 'フィールドを編集',
    edit_form: 'フォーム編集',
    edit_template: 'テンプレートを編集',
    edit_widget: 'ウィジェットを編集',
    error_import_excel: 'インポートに失敗しました。エラーメッセージ：{message}',
    error_import_excel_button: 'インポートを続ける',
    export_bika_file_include_data:
      'bikaファイル（.bika）としてエクスポートすることで、フォルダやデータテーブルなどのリソースをまとめてローカルにバックアップできます。「データテーブルのレコードを含める」オプションを選択すると、bikaファイルのサイズが増加します',
    export_bika_file_title: 'Bika File を导出',
    export_data_include_data: 'データテーブルのレコードを含める',
    export_for_excel: 'Excelにエクスポート',
    features_list: 'ノードリソース一覧',
    field: 'フィールド',
    field_not_found: 'フィールドが見つかりません',
    fields: 'フィールド',
    filter_condition: 'フィルター条件',
    first_field_not_allow_drag: '最初のフィールドはドラッグできません',
    folder_description: 'フォルダ説明',
    folder_empty_description: 'フォルダは空です',
    folder_empty_title: 'フォルダは空です',
    folder_loading_description: '子リソースを読み込んでいます...',
    folder_loading_title: '子リソースを読み込んでいます...',
    folder_name: 'フォルダ名',
    folder_no_content: 'このフォルダには他のフォルダがありません',
    folder_readme: 'フォルダ説明',
    form: {
      add_logo: 'ロゴを追加',
      click_to_view: 'クリックして表示',
      form_description: '説明を入力してください',
      link_to_resource: 'リソースへのリンク:',
      submitted_successfully: '提出が成功しました',
    },
    gallery: {
      cover: 'カバー画像',
      cover_help_text:
        '添付フィールドをカバー画像として選択し、ギャラリービュー内の各カードの上部に表示します。',
      crop_cover_image: 'カバー画像を切り抜く',
      crop_cover_image_help_text:
        '画像は中央に切り抜かれ、カードを埋めるようにします。空白が表示されないようにします。',
      custom_cards_per_row: '行ごとのカード数をカスタマイズ',
      custom_cards_per_row_help_text:
        '行ごとに表示されるカードの数を手動で設定します。デフォルトは自動レイアウト（画面解像度に基づく）。',
    },
    home: 'リソースエディタ',
    import_bika_file_success: 'インポートに成功しました',
    import_bika_file_support: '.bika ファイルのアップロードに対応',
    import_excel_import_button: 'インポート',
    import_excel_records_count:
      'プレビューには先頭 10 行が表示されます。実際のインポートでは {count} 行のデータが含まれます',
    import_excel_step1:
      'ステップ1: テンプレートをダウンロードし、データを入力してください。注意: インポート失敗を防ぐために、テンプレート内のタイトルを変更しないでください。計算フィールド、メンバーフィールド、添付ファイルフィールド、リレーションシップフィールドはサポートされていません。',
    import_excel_step2:
      'ステップ2: 完成した Excel ファイルをここにドラッグしてアップロードします。',
    import_file_for_box: '.bikaファイルからデータをインポートして新しいリソースを作成',
    import_from_excel: 'Excelからインポート',
    import_from_vika: 'Vikaからインポート',
    import_from_vika_helper_text:
      '複数のリソースはコンマで区切られます (例: fold1、fold2)。\n注意: \n1. まず、VIKA アプリケーションが統合されていることを確認します。\n2. テーブルにメンバー フィールドがある場合は、まず Vika プラットフォームから Bika にメンバー情報をインポートしてください。そうしないと、データのインポート後にメンバー フィールドが自動的にクリアされます。\n3. Vika ユーザーと Bika ユーザーは電子メールで関連付けられます。',
    import_from_vika_label: 'Vika リソース ID',
    include_widgets: 'ウィジェットを含める',
    included_resources: '含まれるリソース',
    incremental_import_from_excel: 'Excel からのインクリメンタルインポート',
    kanban: {
      add_kanban_group_card: '新規レコード',
      delete_kanban_tip_content: 'このグループ内のすべてのレコードが未分類のグループに移動されます',
      delete_kanban_tip_title: 'グループの削除',
      editing_group: '[編集](Edit)領域',
      group_already_exists: 'グループはすでに存在します',
      group_by_option_or_member:
        '特定の「メンバー」フィールドまたは「シングルセレクト」フィールドに基づいてレコードをグループ化します。',
      hide_kanban_grouping: 'グループを隠す',
      kanban_add_new_group: '新規グループ',
      kanban_no_data: '記録なし',
      kanban_not_group: '未分類',
      kanban_view_limit:
        'かんばんビューに表示できるレコードの最大数は1000です。レコードをフィルタリングして表示してください。',
      no_multiple_selection_member_field: '複数選択のメンバーフィールドはサポートされていません',
      no_single_choice_or_member_field:
        '現在、単一選択フィールドまたはメンバーフィールドがありません。',
      please_select_single_or_member_field:
        'カンバンビューを作成するために、単一選択フィールドまたはメンバーフィールドを選択してください',
    },
    layout: 'レイアウト',
    layout_help_text:
      'レイアウトは、ビュー内でレコードがどのように配置され表示されるかを定義します。',
    mirror_type_label: {
      database_view: '選擇現有視圖',
      node_resource: '既存のリソースを選択',
      view: '独立した条件',
    },
    month: '月',
    move_resource_error: '移動に失敗しました',
    move_resource_success: '移動が成功しました',
    move_resource_to: '{name} を現在のフォルダーに移動',
    move_resource_to_public_description: '"{name}" をチームリソースに移動してもよろしいですか？',
    move_resource_to_public_error: 'チームリソースへの移動に失敗しました',
    move_resource_to_public_success: 'チームリソースへの移動が成功しました',
    new_field: '新しいフィールド',
    no_cover: 'カバーがありません',
    no_member_field: 'メンバーフィールドがありません',
    no_permission_operation: 'この操作を行う権限がありません',
    no_permission_operation_description:
      '申し訳ありませんが、この操作を行う権限がありません。管理者に通知してください',
    node_detail: 'ノード詳細',
    not_support_import_field:
      'Excel からデータをインポートして新しいデータテーブルを作成します。注意: 計算フィールド、メンバーフィールド、添付ファイルフィールド、リレーションシップフィールドはインポートできません。',
    operation_failed: '操作失敗',
    parsing_excel_data: 'データを解析中',
    placeholder_create_field: '新しいフィールドを作成してください',
    placeholder_no_field: 'フィールドがありません',
    placeholder_no_number_field: '数値型フィールドがありません',
    placeholder_select_field: 'フィールドを選択してください',
    placeholder_select_member_field: 'メンバーフィールドを選択してください',
    placeholder_select_record: 'レコードを選択してください',
    placeholder_select_resource: 'リソースを選択してください',
    placeholder_select_view: 'データベースのビューを選択してください',
    placeholder_select_widget: 'ウィジェットを選択してください',
    placeholder_view_name: 'ビュー名を入力してください',
    record_detail: {
      link_new_record: '新しいレコードをリンク',
      link_record: '既存のレコードをリンク',
      linked_from: '"{name}" の関連レコードから',
      no_linkable_record: 'リンク可能なレコードがありません',
      no_related_records: '関連記録がありません',
      record_detail_not_found: 'レコード詳細が見つかりません',
      select_record: 'レコードを選択',
      tip_refresh:
        '編集中の内容が変更されました。編集した内容を他の場所にコピーして保存し、ページをリフレッシュしてください。',
    },
    record_index: 'レコード {index}',
    remove_field_success: 'フィールドの削除に成功しました',
    remove_folder_description:
      'フォルダーおよびその中に含まれるすべてのリソースが削除されます。削除後は復元できません。本当にフォルダー "{name}" を削除しますか？',
    remove_shortcut_success: 'ショートカットが正常に削除されました',
    required_field: '必須フィールド',
    resource: 'ノードリソース',
    resources: 'ノードリソース',
    set_form_required: '必須として設定',
    set_form_required_description:
      'フィールドを必須として設定すると、ユーザーはレコードを作成または編集する際にそのフィールドに入力する必要があります。',
    success: '成功',
    success_import_excel:
      '{columns} 列のフィールドに {rows} 行のデータが正常にインポートされました',
    success_import_excel_button: '確認する',
    support_upload_file: '.xlsx/.csv ファイルのアップロードに対応',
    template_creator: 'テンプレート作成者',
    template_folder_editor: 'テンプレートフォルダーエディタ',
    title_create_folder: 'フォルダー',
    title_dashboard_id: 'ダッシュボード',
    title_database_id: 'データベース',
    title_delete_resource: 'リソースを削除',
    title_edit_resource: 'リソースを編集',
    title_export: 'リソースをエクスポート',
    title_field_id: 'フィールド',
    title_form_id: 'フォーム',
    title_form_name: 'フォーム名',
    title_import: 'リソースをインポート',
    title_member_field_id: 'メンバーフィールド',
    title_mirror_type: 'ミラー型',
    title_move_resource_to_public: 'チームリソースへ移動',
    title_move_to: '移動先',
    title_new_ai: 'AIエージェント(Beta)',
    title_new_automation: '自動化',
    title_new_computer: 'New Computer(alpha)',
    title_new_dashboard: 'ダッシュボード',
    title_new_database: 'データベース',
    title_new_document: 'ドキュメント',
    title_new_file: 'ファイル',
    title_new_form: 'フォーム',
    title_new_mirror: 'ミラー',
    title_new_other_resource: 'テンプレートから作成',
    title_new_page: 'AI ページ(Beta)',
    title_new_resource: '新しいリソース',
    title_new_view: 'ビュー',
    title_record_id: 'レコード',
    title_resource: 'リソース',
    title_resource_description: 'リソース説明',
    title_resource_id: 'リソース',
    title_resource_name: 'リソース名',
    title_resource_type: 'リソースタイプ',
    title_shortcut: ' ジェクトに追加',
    title_shortcut_cancel: 'ショートカットをキャンセル',
    title_shortcut_personal: '個人ショートカット',
    title_shortcut_personal_remove: '個人ショートカットを削除',
    title_shortcut_space: 'スペースショートカット',
    title_shortcut_space_remove: 'スペースショートカットを削除',
    title_show_hidden_field: '全て表示',
    title_space_admin: '管理者機能',
    title_space_shortcut: 'ショートカットに追加（全員に見える）',
    title_update_folder: 'フォルダを更新',
    title_view_id: 'ビュー',
    title_view_type: 'ビュータイプ',
    toggle_view: 'Toggle View',
    type: {
      ai: 'AI エージェント',
      ai_agent: {
        model: 'AIモデル',
        data_source: 'データソース',
        data_source_description: 'リソースを指定して、エージェントに背景コンテキストを与えます。',
        description:
          '質問を入力すると、AI エージェントがあなたのニーズに基づいて適切なコンテンツとデータを生成します',
        settings_datasource_sitemap_placeholder: 'データソースの Sitemap を入力してください',
        settings_datasource_url_placeholder: 'データソースの URL を入力してください',
        settings_tool_toolsdks_package_key_placeholder: 'Package Key を入力してください',
        settings_tool_toolsdks_package_version_placeholder: 'Package Version を入力してください',
        skillset: 'スキルセット',
        skillset_description:
          'スキルセットでエージェントの能力を拡張し、2000以上のMCPサーバーに対応します。',
        system_prompt_description:
          'AIエージェントの応答に対する指示と制限、質問に対するより良い回答方法を伝えます。',
        system_prompt_placeholder:
          'AIの応答に対する指示と制限。\n以下の文体を参照できます:\n\n# 役割\nあなたは、ユーザーにさまざまな短期旅行や近隣旅行プランを提供し、さまざまな観光地の特徴を生き生きと描写できるプロの旅行アドバイザーです。\n\n# 制限\n\n短期旅行および近隣旅行に関連する内容のみを議論し、無関係なトピックには回答しません。\nすべての出力は、指定された形式に従って整理され、フレームワーク要件から逸脱してはなりません。\n特徴紹介セクションは100語を超えてはなりません。\n',
        title: 'AI エージェント',
        help_doc_link: '/help/guide/ai/ai-agent',
      },
      ai_description: '人工知能機能のためのリソース',
      ai_wizard_description:
        'AIウィザードは、Bika.aiのチャットインターフェースです。プラットフォーム内でさまざまな種類や目的のAI会話に使用できます。',
      ai_wizard_features_list: 'AI Wizard List',
      ai_wizard_intent_ui_description:
        'Bika.aiのAIウィザードでは、さまざまな種類や目的のAI会話が分類され、このカテゴリは「インテント」と呼ばれます。\n        各インテントはシステム内で特定の応答や機能をトリガーするように設計されているため、異なるインテントは異なるアクション結果につながります。',
      ai_wizard_intent_ui_features_list: 'AI Wizard Intent UI List',
      app_page: 'App Page',
      app_page_description: 'App Page',
      automation: '自動化',
      automation_action_description:
        '自動化アクションとは、メールの送信などのタスク、活動、イベント、または変更を実行するステップを指します。\nアクションは次のように考えることができます：何か（トリガー）が発生し、指定された条件が満たされた場合、このイベント（アクション）が実行されます。',
      automation_action_features_list: 'アクション一覧',
      automation_description: '自動化ワークフローを設定して管理するためのリソース',
      automation_trigger_description:
        '自動化トリガーは、特定の条件が満たされたときに自動化を開始する「スイッチ」として機能します。\nトリガーは次のように考えることができます：特定のイベント（トリガー）が発生し、特定の条件が真である場合、結果のイベント（アクション）が実行されます。',
      automation_trigger_features_list: 'トリガー一覧',
      canvas: 'キャンバス',
      canvas_description: '描画とデザインのためのキャンバス',
      chat: 'チャット',
      chat_description:
        'AI ウィザードを使用して、システムと自然言語で会話し、必要な情報とサポートを取得し、作業効率を向上させることができます。',
      chat_menu: {
        fold: '非表示',
        pin: 'ピン留め',
        unpin: 'ピン留め解除',
      },
      code_page: 'AI ページ',
      code_page_description: 'コードページを作成して公開するためのリソース',
      create_node_resource: 'ノードリソースを作成',
      create_node_resource_description:
        'AI ウィザードを使用して、さまざまなノードリソースを簡単かつ迅速に生成および管理し、プロジェクトの計画と管理を容易にします。この機能により、リソースをより効果的に整理および割り当て、プロジェクトの円滑な進行を確保できます。',
      create_record: 'レコードを作成',
      create_record_description:
        'AI ウィザードを使用して、新しいデータレコードを簡単かつ迅速に作成し、情報の正確性とタイムリー性を確保できます。',
      create_reminder: 'リマインダーを作成',
      create_reminder_description:
        'AI ウィザードを使用して、さまざまなタスクやイベントのリマインダーを簡単かつ迅速に設定し、重要なことを見逃さないようにします。特定のニーズに合わせて、リマインダーの時間と内容をカスタマイズできます。',
      dashboard: 'ダッシュボード',
      dashboard_description: '主要なデータを要約して表示するためのダッシュボード',
      database: 'データテーブル',
      database_description:
        'データベースはスプレッドシートに似ていますが、より多用途です。各データベースは行と列で構成されており、行はレコードを表し、列はフィールドを表します。フォルダー内に複数のデータベースを作成して、さまざまなデータタイプを整理および分類できます。データベースは、テキスト、数値、添付ファイル、リンクなど、さまざまなフィールドタイプをサポートしており、多様な情報の保存が可能です。ビューを利用してデータをフィルタリング、並べ替え、グループ化することで、データ管理と分析の効率を向上させることができます。',
      database_field_description:
        'データベースフィールドは、データベーステーブルの各レコードの詳細やメタデータを含みます。\n        データベースフィールドは、データベーステーブルの各エントリの情報やメタデータを保持します。これらのフィールドはさまざまな形式を取ることができ、テキスト、単一または複数の選択肢、画像、チェックボックス、数値、ユーザータグなどとしてデータを保存できます。',
      database_field_features_list: 'データベースフィールド一覧',
      database_view_description:
        'データベースビューは、データベーステーブルの基礎となるデータを視覚化および整理する特定の方法を提供します。\n        標準的なビューはグリッドですが、フォーム、カレンダー、ギャラリー、かんばんレイアウトなどの他の形式も含まれます。\n        1つのデータベースで複数のビューとさまざまな種類のビューをサポートできます。',
      database_view_features_list: 'データベースビュー一覧',
      doc: 'ドキュメント',
      doc_description: 'ドキュメントの作成と保存のためのリソース',
      file: 'ファイル',
      file_description: '純粋なファイルを保存および管理するためのリソース',
      folder: 'フォルダー',
      folder_description: 'ファイルを保存および管理するフォルダ',
      form: 'フォーム',
      form_description:
        'フォーム機能により、指定されたデータテーブルにデータを収集および入力するためのカスタムフォームを作成できます。データテーブルのビューを指定することで、すばやくフォームを生成し、さまざまなソーシャルグループに共有できます。送信されたデータは自動的に対応するテーブルに更新され、管理および分析が容易になります。フォーム機能は、テキスト、添付ファイル、チェックボックスなど、さまざまなフィールドタイプをサポートしており、さまざまなデータ収集ニーズに対応します。',
      integration_description:
        '連携は、Bika.aiと外部サービスやアプリケーションとの接続であり、両プラットフォーム間でのシームレスなデータ転送を可能にします。\n        選択する連携は、データで解決したい特定の問題によって異なります。\n        例えば、タスクを追跡するデータベースレコードがあり、要約のためにAIを活用したい場合、OpenAI連携を利用してデータをOpenAIに送信し、返された情報を使用してメールを送信することができます。',
      mirror: 'ミラー',
      mirror_description: 'データを同期して反映するためのリソース',
      report_template: 'レポートテンプレート',
      report_template_description: 'レポートを作成して管理するためのテンプレート',
      view: 'ビュー',
      view_description: 'データを表示して閲覧するためのリソース',
      web_page: 'ウェブページ',
      web_page_description: 'ウェブページを作成して公開するためのリソース',
    },
    unbind_template_modal_content:
      'テンプレートを解除した後は、このファイルを自由に変更できますが、テンプレートのその後の更新を受け取ることはできません',
    unbind_template_modal_title: 'この操作を行う前にテンプレートを解除する必要があります',
    update_automation_action_success: 'アクション "{name}" を正常に更新しました',
    update_automation_success: '自動化 "{name}" を正常に更新しました',
    update_dashboard_success: 'ダッシュボード "{name}" を正常に更新しました',
    update_database_success: 'データベース "{name}" を正常に更新しました',
    update_folder_success: 'フォルダー "{name}" を正常に更新しました',
    update_view_success: 'ビュー"{name}"の更新に成功しました',
    view_count: '{count} ビュー',
    view_hidden_all_field: ' すべての列を非表示',
    view_hidden_field: '列を隠す',
    view_name: 'ビュー名',
    view_show_all_field: 'すべての列を表示',
    view_show_field: '列を表示',
    view_type: {
      form: 'フォーム',
      gantt: 'ガントチャート',
      grid: 'グリッド',
      kanban: 'カンバン',
    },
    views: 'ビュー',
    week: '週',
    widget_setting: 'ウィジェットの設定',
    widgets: {
      description:
        'ウィジェットは、データベース、ビュー、フォーム、ダッシュボードなどのリソースに関連する情報を表示するための小さなアプリケーションです。ウィジェットは、データの要約、分析、および可視化をサポートし、データの理解と意思決定を促進します。ウィジェットは、テキスト、数値、グラフ、チャート、カレンダー、リストなど、さまざまなデータ表示形式をサポートしており、さまざまなデータ分析ニーズに対応します。',
      features_list: 'ダッシュボードウィジェット一覧',
      name: 'ウィジェット',
    },
  },
  role: {
    allow: '許可',
    msg_create_role_success: 'ロールが正常に作成されました',
    msg_delete_role_error: '{name}の削除に失敗しました、{message}',
    msg_delete_role_success: '{name}を正常に削除しました',
    msg_load_role_error: 'ロール情報の読み込みに失敗しました',
    msg_update_role_success: 'ロールが正常に更新されました',
    role: '役割',
    roles: '役割',
    select_role: '役割を選択',
  },
  scheduler: {
    daily_base: 'デイリー',
    friday: '金曜日',
    hourly_base: '時間給',
    last_day: '月の最終日',
    minute_base: '毎分',
    monday: '月曜日',
    monthly_base: '月',
    repeat_frequency: '繰り返し頻度',
    repeat_interval: '繰り返し間隔',
    repeat_per_monthday: '月ごとに繰り返し',
    repeat_per_weekday: '曜日ごとに繰り返し',
    saturday: '土曜日',
    started_at: '開始時間',
    sunday: '日曜日',
    thursday: '木曜日',
    timezone: 'タイムゾーン',
    tuesday: '火曜日',
    wednesday: '水曜日',
    weekly_base: 'ウィークリー',
    yearly_base: '毎年',
  },
  settings: {
    about: {
      about_brand: 'Bika.aiについて',
      help_center: 'ヘルプセンター',
    },
    account: {
      account: 'アカウント',
      account_information: 'アカウント情報',
      api: '開発者',
      api_description: '開発者 API トークンは安全に保管してください。',
      connected_account: '接続アカウント',
      connected_account_description:
        '下のプラットフォームのアカウントを連携して、Bikaに簡単にログインできます。',
      login_record_description:
        'システム内のログイン アクティビティ情報は、アカウントのセキュリティを保護し、より良いユーザー エクスペリエンスを提供するために記録されます。',
      notification: '通知',
      referral: '紹介',
      session_logs: 'セッションログ',
      top_up: 'チャージ',
      unbind: 'バインド解除',
      unbind_description: 'このアカウントを解除してもよろしいですか？',
    },
    audit: {
      action: 'アクション',
      action_name: {
        invitation_email_accept: '招待を承諾',
        invitation_email_delete: '招待メールを削除',
        invitation_email_resend: '招待メールを再送',
        invitation_email_send: '招待メールを送信',
        invitation_link_accept: '招待リンクを承諾',
        invitation_link_create: '招待リンクを作成',
        invitation_link_delete: '招待リンクを削除',
        node_create: 'リソースを作成',
        node_delete: 'リソースを削除',
        node_detach: 'テンプレートを分離',
        node_export: 'リソースをエクスポート',
        node_get: 'リソースをアクセス',
        node_import: 'リソースをインポート',
        node_publish: 'リソースを公開',
        node_update: 'リソースを更新',
        node_upgrade: 'テンプレートをアップグレード',
        share_grant: 'リソースを許可',
        share_password_create: '共有にパスワードを追加',
        share_password_delete: '共有のパスワードを削除',
        share_password_update: '共有のパスワードを更新',
        share_restore: 'リソースを復元',
        share_revoke: 'リソースを取り消し',
        share_scope_update: '共有範囲を更新',
        share_upsert: 'リソースを共有',
        space_update: 'スペースを更新',
        template_install: 'テンプレートをインストール',
      },
      actor: 'アクター',
      description: '説明',
      empty: 'まだ監査ログがありません',
      page_info: '{total} 件のイベントのうち、{size} 件を表示しています',
      search_empty: '検索結果が見つかりません',
      template: {
        invitation_email_accept: '{email} の招待を承諾しました',
        invitation_email_delete: '{email} のメールを削除しました',
        invitation_email_resend: '{email} に再送しました',
        invitation_email_send: '{emails} に招待を送りました',
        invitation_link_accept: "<a href='/space/join/{token}'>招待リンク</a>を承諾しました",
        invitation_link_create: "<a href='/space/join/{token}'>招待リンク</a>を作成しました",
        invitation_link_delete: "<a href='/space/join/{token}'>招待リンク</a>を削除しました",
        node_create:
          "{parent} にリソースを作成しました <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_delete: 'リソース {name} を削除しました',
        node_detach: "リソースを分離しました <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_export:
          "リソースをエクスポートしました <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_get: "リソースにアクセス <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_import: "リソースをインポートしました <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_publish: "リソースを公開しました <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_update: "リソースを更新しました <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_upgrade:
          "テンプレートフォルダをアップグレードしました <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        share_grant: '{units} にリソース {name} へのアクセス権を付与しました',
        share_password_create: '共有リソース {name} へのアクセス用パスワードを作成しました',
        share_password_delete: '共有リソース {name} へのアクセス用パスワードを削除しました',
        share_password_update: '共有リソース {name} へのアクセス用パスワードを更新しました',
        share_restore: 'リソース {name} へのアクセス権を復元しました',
        share_revoke: '{unit} のリソース {name} へのアクセス権を取り消しました',
        share_scope_update: 'リソース {name} の共有範囲を更新しました',
        share_upsert: 'Create or update share link for resource {name}',
        space_update: 'がスペースステーションを更新しました',
        template_install:
          "がテンプレートをインストールしました (<a href='/space/{spaceid}/node/{nodeid}'>{name}</a>)",
      },
      time: '時間',
    },
    billing: {
      already_support: 'ロック解除済み',
      apply_refund: '返金を申し込む',
      cancel_subscribe: 'サブスクリプションをキャンセルする',
      cancel_subscription_content: 'サブスクリプションをキャンセルしてもよろしいですか？',
      cancel_subscription_tips:
        'キャンセルが成功しました。現在の請求期間が終了するまで、すべての機能に完全にアクセスできます',
      cancel_subscription_title: 'サブスクリプションをキャンセルする',
      canceled_at: 'キャンセル時間',
      change_payment_method: '支払い方法を変更する',
      change_plan: 'プランを変更する',
      current_plan: '現在のプラン',
      manage: '請求管理',
      next_invoice_date: '次回の請求日',
      no_billing_day: 'なし',
      not_support: '対応していません',
      payment_status: {
        buying_plan: '{plan}プランを購入しています',
        guest_limit: '🌟 1000 guests',
        payment_failed: 'Payment failed',
        payment_failed_description: 'Sorry, subscription to {plan} plan failed, please try again',
        privileges_gained: 'You have gained the following privileges:',
        storage_limit: '🌟 200GB storage space',
        try_again: 'Try again',
        try_now: 'Try now',
        unlimited_members: '🌟 Unlimited members',
        upgrade_success: 'Congratulations on upgrading to {plan} successfully!',
        waiting_payment: '支払いを待っています',
      },
      resume_subscription: 'Resume Subscription',
      start_now: '今すぐ開始',
      usage: '使用量 & 請求書',
      usage_detail: '使用量の詳細',
      usage_limit_template:
        '申し訳ありません。このスペースステーションの<strong>{feature}</strong>の上限は<strong>{max}</strong>で、すでに<strong>{current}</strong>を使用しています。上限を引き上げるには、プランのアップグレードをご検討ください。',
      usage_tips: '購入の支援が必要な場合はお問い合わせください',
      usages: {
        api_request: '1 か月あたりの API リクエスト数',
        automation_runs: '1 か月あたりの自動化実行数',
        emails: 'メール送信数',
        guests: '訪問者数',
        missions: '毎月の To Do タスクの合計',
        records: 'ステーション記録数',
        reports: '月次合計レポート',
        resource_permission: '割り当て可能な権限リソースの数',
        resources: 'ノードリソース数',
        seats: '席数',
        space_integrations: '統合例',
        storages: '添付ファイルの容量',
        unused: '未使用',
        used: '使用済み',
      },
      wating_payment: '支払いを待っています',
      will_cancel_subscribe:
        'ご利用のプランは {expireAt} まで有効で、その後は自動的に無料プランに変更されます。',
      you_will_lose_all_privileges:
        'サブスクリプションをキャンセルすると、すべての特権が失われます',
    },
    coin_rewards: {
      all_plan: 'すべてのプラン',
      all_plan_description: 'ダウングレードまたは解約した場合、返金は自動的に処理されません。',
      and: 'そして',
      choose_plan: 'プランを選択',
      coin_description: '使用可能',
      copy_invitation_code: '招待コードをコピー',
      copy_invitation_link: '招待リンクをコピー',
      current_plan: '現在のプラン',
      get_coin_history: '報酬履歴',
      get_rewards_methos_1:
        '方法1：招待リンクを使用して新しいユーザーを招待すると、両方が報酬を得ることができます',
      get_rewards_methos_2:
        '方法2：招待コードを入力していないユーザーにあなたの招待コードを提供すると、両方が報酬を得ることができます',
      get_rewards_methos_3: '方法3：メンバーをスペースステーションに招待して、両方が獲得',
      get_rewards_methos_4: '方法4：アプリをインストールして、あなたが獲得',
      invite_member_rewards_coin: 'メンバーを招待してポイントを獲得',
      no_coin_history: '報酬履歴はありません',
      plan_unit: 'メンバーごと/月',
      purchase_advanced_mode: 'プレミアム・モードの購入',
      recommend_get_bika_coin: '推薦賞ポイント',
      to_invite_member: 'メンバーを招待する',
      upgrade_space: 'スペースステーションのアップグレード',
      view_usage: '使用状況を見る',
      your_coin: 'あなたのポイント',
    },
    general: '一般',
    guest: {
      create_guest_team: 'ゲストチームを作成',
      delete_guest_team_description:
        'ゲストチームを削除すると元に戻せません。本当にこのゲストチームを削除しますか？',
      delete_guest_team_success: 'ゲストチームを削除しました',
      delete_guest_team_title: 'ゲストチームを削除',
      edit_guest_team: 'ゲストチームを編集',
      no_guest: '訪客はいません',
      select_guest: 'ゲストを選択',
      select_guest_team: 'ゲストチームを選択',
    },
    member: {
      assign_members: 'メンバーを割り当てる',
      choose_member_role: 'メンバーロールを選択（任意）',
      choose_member_role_placeholder: '役割を選択してください',
      choose_member_team: 'メンバーチームを選択',
      choose_member_team_placeholder: 'チームを選択してください',
      delete_member: 'メンバーを削除',
      delete_member_confirm: 'メンバーを宇宙ステーションから移動させてもよろしいですか?',
      description: '説明',
      edit_error: '編集失敗',
      edit_member: 'メンバーを編集',
      edit_success: '編集成功',
      empty_email: '利用可能なメールがありません',
      member_name_setting_description:
        'メンバー名は、スペースステーション内の内部で使用されるニックネームであり、異なるスペースステーション間でも変更されません',
      member_or_group_not_found: 'メンバー見つからない',
      remove_member: 'メンバーを削除',
      remove_member_confirm: '選択したメンバーをこのロールから削除してもよろしいですか？',
      remove_member_confirm_content:
        '選択したメンバーは一部の権限を失う可能性があります。本当にこのロールから削除しますか？',
      remove_members: '{count}人のメンバーを削除',
      set_space_member_name: 'スペースのニックネームを設定する',
    },
    message_member_name_modified_failed: 'メンバー名を編集できません',
    message_member_name_modified_successfully: 'メンバー名を編集しました',
    nickname_modified_failed: 'ニックネームを編集できません',
    notification: {
      advertise:
        'より迅速な通知と特別な体験を得るために、Bika.ai のモバイルアプリをダウンロードすることをお勧めします',
      description:
        'Bikaは、タスク、スケジュール、およびレポートの最新状況をリアルタイムで把握できるように、さまざまな通知方法を通じてリマインダーメッセージを送信することをサポートしています。',
      email: {
        description: '連携後、電子メールで通知を受け取ることができます',
        title: 'メール通知を受け取る',
      },
      push: {
        description: 'ブラウザ通知を受け取るには、連携が必要です',
        title: 'ブラウザ通知を受け取る',
      },
      sms: {
        description: '連携後、SMS通知を受け取ることができます',
        title: 'SMS通知を受け取る',
      },
      title: '通知',
      wechat_push: {
        setting: '設定',
        title: 'WeChat公式アカウント通知',
      },
    },
    other: 'その他',
    role: {
      confirm_delete_role: 'このロールを削除してもよろしいですか？',
      create_role: '役割を作成する',
      create_role_empty_error: 'ロール名を空にすることはできません。',
      create_role_placeholder: '役割名を入力してください',
      delete_role: '役割を削除する',
      delete_role_confirm_content: 'ロールを削除すると元に戻せません。本当に削除しますか？',
      delete_roles: '${count} 個の役割を削除',
      deselect: '選択解除',
      edit_role: '役割を編集する',
      edit_role_error: '編集できません、ページを更新してください',
      edit_role_placeholder: '役割名を入力してください',
      management_role: '管理役割',
      member_no_role_tips: '現在、この役割に就いているメンバーはいない',
      need_select_a_role: '役割を選択してください',
      need_select_role: '役割を選択してください',
      non_management_role: '非管理役割',
      role_name: '役割名',
      role_permission: '権限の役割',
      role_type: '役割の種類',
      select: '選択',
    },
    space: {
      announcement_description:
        '告知内容はスペースステーションのホームページに表示され、全てのメンバーが入室時に見ることができます。',
      announcement_placeholder:
        'アナウンスの内容はスペースステーションのホームページに表示され、すべてのメンバーがスペースステーションに入ると見ることができます。また、Markdown形式で編集することができ、アナウンスの可読性と美観を向上させます。。',
      authorized_domain: '認証済みメールドメイン',
      authorized_domain_description:
        '許可されたメールドメインを設定して、スペースステーションへのアクセスを制限します。これらのドメインからのメールアカウントのみが参加でき、その他は拒否されます。',
      authorized_domain_settings: '認証済みメールドメイン設定',
      config_watermark: '透かしを',
      create_and_go: '作成して移動',
      create_mission: 'ミッションを作成',
      create_mission_description:
        '自動化設定を使用して、さまざまな種類の新しいタスクをトリガーすることができます。タスクの担当者、ノードリソース、期限日などの詳細を設定することが可能です。タスク作成後、設定された時間とリソースに基づいて自動的にトリガーされ、AIによってタスク管理および追跡が行われ、効率的なタスクの完了が確保されます。',
      create_space_description: 'どんな名前にしたいですか？',
      current_permission: '現在のスペースステーションのデフォルト権限は',
      current_permission_tips:
        'これは、誰かが新しいリソースを作成し、権限を割り当てない場合、スペースステーション内のすべてのメンバーがデフォルトで持つことを意味します',
      default_space_name: 'マイスペース',
      delete_space: 'スペースの削除',
      delete_space_confirm_desc:
        'この操作は元に戻せません！誤操作を避けるため、スペースID {spaceId} を入力して確認してください。削除後、すべてのデータは永久に失われます。',
      delete_space_confirm_title: 'スペースの削除',
      delete_space_desc:
        'スペースステーションを削除すると、スペースステーション内のすべてのデータ（ノードリソース、添付ファイルなど）が永久に削除され、復元できなくなります',
      delete_space_success: 'スペースステーション削除が成功しました',
      description:
        'スペースは、特定の目的や目標に基づいて作成された仮想ワークスペースです。スペースステーションは、スペース内のリソースを管理し、チームメンバーとの協力を促進するために設計されています。',
      disable_global_manage_resource:
        'ルートノード下でのメンバーによるリソースの作成、アクセス、操作権限を制御する',
      disable_global_manage_resource_description:
        'メンバーがスペースに参加する際のリソースの初期アクセス権限を制御します。無効にすると、すべてのメンバーにデフォルトで「管理可能」権限が付与されます。',
      disable_global_manage_resource_learn_more: 'ヘルプドキュメントに移動してさらに詳しく知る',
      invite_members: 'メンバーを招待',
      ip_address_placeholder: 'IPアドレスを入力してください',
      join: '参加する',
      member_management: 'メンバー管理',
      member_management_description:
        'メンバー管理は、チームメンバーを効率的に管理するための機能です。このモジュールを使用すると、メンバー情報の追加、削除、編集が可能で、各メンバーに適切な役割と権限を割り当てることができます。メンバー管理モジュールには検索およびフィルタリング機能も備わっており、特定のメンバーを迅速に見つけることができます。さらに、グループを作成および管理し、メンバーをグループに追加することで、より詳細な組織構造の管理が可能です。',
      mission: 'ミッション',
      mission_description:
        'AIによって割り当てられた特定のタスクまたは目標で、通常は自動化されたワークフローの一部です。Bika.aiでは、設定に基づいてAIがタスクを自動生成し、完了するように促します。',
      permission_settings_description: 'スペースステーションリソース管理',
      placeholder_authorized_domain: '認証されたメールドメインを入力してください。例：example.com',
      please_input_space_id: 'スペースステーションのIDを入力してください',
      request_new_record: 'リクエスト記録',
      request_new_record_description:
        'システムに送信されたリクエストのログで、タイムスタンプやステータスなどの詳細情報を含みます。Bika.aiでは、オートメーションタスクやユーザー入力を追跡するためにこれを使用します。',
      rich_document: 'リッチテキスト',
      rich_document_description:
        'テキスト、画像、その他のメディア要素をサポートするテキスト形式で、コンテンツの表示を強化します。Bika.aiは、レポート作成や自動化のためにさまざまなドキュメント形式を統合できます。',
      role_management: '役割管理',
      role_management_description:
        'ロール管理は、権限を効率的に管理および割り当てるための機能です。テンプレートをインストールする際、一部のテンプレートにはいくつかのロールが事前設定されています。これらのロールにメンバーを追加することで、テンプレートの設定をよりスムーズに行うことができます。さらに、カスタムロールを自由に作成し、任意の部門やメンバーを1つまたは複数のロールに割り当てることができ、より柔軟な権限管理が可能です。ロール管理を通じて、各メンバーが適切な権限を持つことを保証し、チームの協力効率を向上させることができます。',
      setting_info: '設定情報',
      setting_info_description:
        '設定情報は、個人設定を開いたときに表示される設定インターフェースです。ここでは、個人情報の更新、テーマの選択、タイムゾーンの設定、システム言語の構成ができます。このインターフェースは、ユーザーが好みに応じてカスタマイズし、よりパーソナライズされた体験を得るのに役立ちます。',
      space: 'スペース',
      space_audits: '監査ログ',
      space_create_failed: 'スペースの作成に失敗しました。もう一度お試しください',
      space_created_success: 'スペースが正常に作成されました！',
      space_has_be_deleted: '空間ステーションは削除されました',
      space_has_be_deleted_desc:
        '申し訳ありませんが、表示する権限がありません。管理者に通知してください',
      space_name_required: 'スペース名を入力してください',
      space_settings: 'スペース設定',
      space_sidebar: 'スペースステーション サイドバー',
      space_sidebar_description:
        'スペースステーション サイドバーは、クイックナビゲーションを提供し、スペースステーション内のさまざまな機能やモジュールに簡単にアクセスできるようにします。サイドバーを通じて、ホームページ、タスク、レポート、リソース、設定などを表示でき、操作が簡素化され、作業効率が向上します。サイドバーは、シンプルな操作体験を提供し、チームの協力を円滑にするために設計されています。',
      third_party_integration: 'サードパーティー連携',
      upgrade: 'アップグレード',
      usage: '使用量',
      wallpaper_button: '壁紙を設定',
      wallpaper_description:
        '壁紙はスペースステーションのホームページに表示され、すべてのメンバーが入室時に見ることができます。',
      wallpaper_preset_photos: 'プリセット壁紙',
      wallpaper_title: 'ホームページの壁紙',
      watermark_description: 'グローバルウォーターマーク',
      watermark_description_2:
        '企業情報の安全を確保するために、リソースはグローバル透かしの表示をサポートしています。透かしの内容は、現在アクセスしているメンバーの名前+電話番号のサフィックスまたはメールアドレスのプレフィックスであり、スクリーンショットによる情報漏洩を防ぎます',
      workflow: 'ワークフロー',
      workflow_description:
        '特定の結果を達成するためのタスクやプロセスの一連です。Bika.aiでは、ワークフローはAI自動化によって管理され、さまざまな機能にわたる繰り返しタスクが簡素化されます。',
    },
    upgrade: {
      action_record: '詳細',
      ai_invoke_consume: 'AI invoke consume',
      ai_invoke_count_达标奖励: 'AI invoke count达标奖励',
      benefit_details: '特典の詳細を見る',
      bkc: 'BKCを控除する',
      bkc_deduction: 'BKCの控除',
      cancel_subscription: 'サブスクリプションをキャンセル',
      consumption_log: '使用状況',
      credit: 'Space credit',
      credit_desc:
        'Space credit is a credit that users can get through subscription, invite members, and consume credits, which can be used to exchange space services.',
      currency: '通貨の種類',
      currently_owns: '現在所有している',
      cycle: 'サブスクリプション期間',
      cycle_descritpion: 'プランはキャンセルされるまで毎月自動的に更新されます。',
      date: '日付',
      detail: '詳細',
      get_more_bika: '更多のポイントを獲得する',
      gift_credit: '毎日',
      gift_credit_desc:
        '公式から毎日贈られる無料クレジットです。ログインするだけで獲得でき、毎日自動更新されます。',
      invite_logup: '招待登録',
      invite_member: '招待',
      invite_people: '友達を招待する',
      invite_space: 'スペースステーションに参加するための招待',
      loading_tips: '価格を計算中です、お待ちください～',
      member: '成员',
      or: 'または',
      other_method_bkc: '他の方法でも',
      pay_annual: '年払い',
      pay_monthly: '月払い',
      pay_now: '今すぐ支払う',
      pay_tips: '続行することで、Bika.aiの利用規約に同意したことになります。',
      payment: '支払い方法の選択',
      permanent_credit: '贈与',
      permanent_credit_desc: '公式イベントやメンバー招待などの方法で獲得したポイントは。',
      plan: 'サブスクリプションプラン',
      quanty: '座席数',
      recharge_consumption_integral: 'クレジットの変更',
      resume_subscription: 'サブスクリプションを再開',
      serial_number: '連番',
      space: 'スペースステーションの名前',
      space_member_num: 'スペースステーションのメンバー数',
      subscribe_credit: 'サブスクリプション',
      subscribe_credit_desc:
        'ワークスペースのサブスクリプションプランによって決定され、サブスクリプション後に対応するワークスペースポイントを獲得し、サブスクリプション日に基づいて毎月自動的に更新されます。',
      subtotal: '小計',
      total: '合計',
      unit_price: '単価',
      user_referral: 'User referral',
      user_topup: 'ユーザーのトップアップスペース',
    },
  },
  share: {
    already_set_permission: '権限が制限されており、親フォルダの権限を引き継ぐことはできません',
    change_permission_success: '権限が正常に変更されました',
    change_pwd: 'パスワードを変更',
    change_pwd_success: 'パスワードが変更されました',
    change_share_pwd: '共有パスワードを変更',
    close_short_link_warning: '短いリンクを無効にすると、下のリンクが無効になります',
    copy_pwd_and_link: 'リンクとパスワードをコピー',
    copy_pwd_and_link_success: 'リンクとパスワードをコピーしました',
    create_short_link_warning: '短いリンクを作成し、下の共有リンクが無効になります',
    has_num_member_share: '{memberCount}人が共有中',
    member_of_share_permission: '共有権限のメンバー',
    network_user_need_pwd:
      'インターネットユーザーはパスワードが必要ですが、組織内ユーザーはパスワードなしでアクセスできます',
    open_pwd: 'パスワードを有効にする',
    permission: {
      input_pwd: 'パスワードを入力',
      no_login_visit: '権限がないか、ログインしていない可能性があります',
      no_permission_visit: 'このリソースを表示する権限がありません',
      no_pwd_visit:
        'このリソースにはパスワードが設定されているため、パスワードを入力する必要があります',
      notify_admin: '管理者に通知',
      notify_admin_for_permision:
        '申し訳ありませんが、閲覧権限がありません。管理者にお知らせください',
      notify_admin_success: 'すでに通知しました！少々お待ちください！',
      publish_to_the_community: 'コミュニティに公開する',
      replay_mode: 'リプレイモード',
      right_pwd: 'パスワードが正しい',
      share_permission_can_edit: 'インターネットでリンクを持つ人は編集可能',
      share_permission_can_view: 'インターネットでリンクを持つ人は閲覧可能',
      share_permission_default: 'メンバーまたは訪問者のみがアクセスできます',
      share_permission_form_anonymous_write: 'リンク訪問者はログインせずに提出できます',
      share_permission_form_login_write: 'リンク訪問者はログイン必須で提出',
      wrong_pwd: 'パスワードが間違っています',
    },
    pwd_pattern: '新しいパスワードを入力してください',
    recover: '回復',
    recover_permission_success: '権限を回復しました',
    set_share_pwd: '共有パスワードを設定',
    share: '共有と権限',
    share_text: '共有',
    ai_conversation: 'AI 会話',
  },
  shortcuts: {
    no_pin_so_far:
      'まだショートカットはありません。早速、よく使うファイルをショートカットに設定してください',
    pin_to_top: '上部にピン留め',
    pinned: 'ピン留めしました',
    shortcuts: '近道',
    unpin_from_top: '上部からピン留め解除',
  },
  skillset: {
    page_description:
      'Bika.ai のスキルセットには、MCP サーバー、サードパーティアプリ、および AI エージェントと自動化ワークフローに使用できる統合機能が含まれています。',
    page_title: 'スキルセット、アプリ、統合機能',
    bika_document: {
      create_document: 'ドキュメントを作成',
    },
    bika_search: {
      bika_search_pages: 'Bika ページ検索',
      bika_search_images: 'Bika 画像検索',
    },
    bika_research: {
      bika_company_research: 'Bika 企業調査',
    },
    bika_office: {
      generate_markdown_document: 'Markdown ドキュメントを生成',
      generate_slides: 'スライドを生成',
    },
    bika_database: {
      list_records: 'レコードを一覧表示',
      aggregate_records: 'レコードを集計',
      create_record: 'レコードを作成',
      get_database_detail: 'データベースの詳細を取得',
      get_fields_schema: 'フィールドスキーマを取得',
    },
    bika_automation: {
      run_automation: '自動化を実行',
      get_automation_detail: '自動化の詳細を取得',
    },
    twitter: {
      send_direct_message: 'ダイレクトメッセージを送信',
      reply_tweet: 'ツイートに返信',
      create_tweet: 'ツイートを作成',
      search_recent_tweets: '最近のツイートを検索',
    },
    custom_mcp_server: 'カスタム MCP サーバー',
    bika_datasets: {
      companies: '企業',
      people: '人',
    },
    bika_space: {
      search_everything: 'すべてを検索',
      get_node_info: 'ノード情報を取得',
      list_nodes: 'ノードを一覧表示',
      list_members: 'メンバーを一覧表示',
      list_teams: 'チームを一覧表示',
      list_roles: '役割を一覧表示',
      list_users: 'ユーザーを一覧表示',
    },
    bika_ai_page: {
      generate_html_page: 'HTML ページを生成',
      ask_for_apply_ai_page: 'AI ページの申請を問い合わせ',
    },
    bika_super_agent: {
      search_launcher_commands: 'ランチャーコマンドを検索',
      search_node_resources: 'ノードリソースを検索',
      search_help_center: 'ヘルプセンターを検索',
      search_members_users: 'メンバー/ユーザーを検索',
      search_database_records: 'データベースレコードを検索',
      search_documents: 'ドキュメントを検索',
    },
    bika_email: {
      send_email: 'メールを送信',
    },
    bika_image: {
      generate_image: '画像を生成',
    },
    everything_mcp_server: {
      add: '追加',
      echo: 'エコー',
      print_env: '環境を出力',
      sample_llm: 'サンプル LLM',
      get_tiny_image: '小さな画像を取得',
      annotated_message: '注釈付きメッセージ',
      get_resource_links: 'リソースリンクを取得',
      start_elicitation: '引出しを開始',
      structured_content: '構造化コンテンツ',
      get_resource_references: 'リソース参照を取得',
      long_running_operation: '長時間実行される操作',
    },
    fetch_mcp_server: {
      fetch: 'フェッチ',
    },
  },
  slogan: {
    alternatives: [
      {
        name: 'Airtable',
        url: 'https://airtable.com/',
        description:
          'Airtableと比較して、Bika.aiはAI自動化と積極的なサポートにより重点を置いています。Bika.aiは、仕事や生活でより多くの自動化とAIの支援を必要とするユーザーに適しています。',
      },
      {
        name: 'Zapier',
        url: 'https://zapier.com/',
        description:
          'Zapierと比較して、Bika.aiはプラグアンドプレイのテンプレートとデータベースワークフローにより重点を置いています。Bika.aiは、仕事や生活でより多くの自動化とAIの支援を必要とするユーザーに適しています。',
      },
      {
        name: 'Make',
        url: 'https://www.make.com/',
        description:
          'Compared to Make, Bika.ai provides more integrated AI-driven solutions and proactive automation directly within its platform. Bika.ai is ideal for users seeking deep automation with advanced AI capabilities to streamline complex workflows and data management tasks.',
      },
    ],
    highlights: [
      {
        icon: '/assets/icons/highlights/auto-template.png',
        name: 'AI従業員のTeams / Slack',
        description: '微信のように協力し、構築し、スマートなAI組織を管理します。',
        keywords: 'AI従業員, AIエージェント, スマート組織, コード不要, 協力プラットフォーム',
      },
      {
        icon: '/assets/icons/highlights/ai-automation.png',
        name: '世界最大のアプリ統合プラットフォーム',
        description:
          '1万以上のMCPツールに接続またはカスタマイズできます。デフォルトのスキルツールには、検索（ウェブ、画像）、研究およびオフィスツール（スライド、文書、スプレッドシート）などが含まれます。',
        keywords: 'アプリ統合, ツール接続, 自動化ワークフロー, MCPツール',
      },
      {
        icon: '/assets/icons/highlights/data-visual.png',
        name: '超強力なノーコードワークステーション',
        description:
          '豊富なノーコードコンポーネント、億単位のビッグデータの多次元テーブル、自動化されたワークフロー、即時共同編集ドキュメント、ダッシュボードなどがすべて一つに集約されています。OpenAPIと互換性があり、拡張可能です。',
        keywords: 'ノーコード, ワークステーション, 多次元テーブル, 自動化ワークフロー, 即時協力',
      },
      {
        icon: '/assets/icons/highlights/auto-publish.png',
        name: 'AIエージェントストア',
        description:
          'あなた自身のスマートAIテンプレートとAI従業員を作成し、公開してコミュニティと共有します。',
        keywords: 'AIエージェント, テンプレートストア, コミュニティ共有, 代理AI',
      },
    ],
    keywords:
      'AI オーガナイザープラットフォーム, ノーコード AI 自動化, エージェンティック AI チーム構築, AI マーケティング自動化, AI リード管理',
    personas: [
      {
        name: 'マーケティング担当者',
        description: 'マーケティング担当者',
      },
      {
        name: 'KOLコンテンツクリエーター',
        description: 'KOLコンテンツクリエーター',
      },
      {
        name: 'オートメーションコンサルタント',
        description: 'オートメーションコンサルタント',
      },
      {
        name: 'プロジェクトマネージャー',
        description: 'プロジェクトマネージャー',
      },
      {
        name: 'セールスマネージャー',
        description: 'セールスマネージャー',
      },
    ],
    screenshots: ['/assets/blog/what-is-bika-ai/template.en.gif'],
    slogan_mkt_e:
      'Bika.ai はビジネス AI エージェントプラットフォームで、AI エージェントによって駆動される CRM、マーケティング自動化システム、プロジェクト管理システム、ビジネスインテリジェンス（BI）、そして企業資源計画（ERP）を提供しています。これらすべてが驚くべき価格で提供されています。',
    slogan_mkt_s: '数秒でAIチームを構築',
    slogan_prd_e:
      'Bika.ai は Airtable（データベース）と Zapier（自動化）を組み合わせた簡単で使いやすいプラットフォームで、AIエージェント強化のCRM、マーケティング自動化システム、プロジェクト管理システム、ビジネスインテリジェンス（BI）およびエンタープライズリソースプランニング（ERP）を提供します。これらすべてを驚くほどの価格で提供します。',
    slogan_prd_l:
      'Bika.ai はビジネス AI エージェントプラットフォームで、能動的な AI 自動化により、様々な仕事を支援します',
    slogan_prd_m: 'ビジネス AI エージェントプラットフォーム、AI に能動的に様々な仕事を行わせます。',
    slogan_prd_xl:
      'Bika.ai はビジネス AI エージェントプラットフォームで、ノーコード、マルチディメンショナルテーブル、データミドルウェア、企業向け AI 知識ベースを融合させ、AI に能動的に様々な仕事を行わせます。AI との繰り返しの対話は必要ありません。Bika.ai は繰り返し作業を自動化し、マーケティングや営業など、様々な職能においてシームレスに実行することができ、あなたに戦略的な仕事に集中することを可能にします。',
    slogan_title:
      'Bika.ai: AIスマート組織プラットフォーム、あなたのAIスマート社員チームを構築します',
    use_cases: [
      {
        name: 'マーケティング自動化',
        description:
          'メール、YouTube動画、Twitterツイート、通知SMSなどのマーケティングコンテンツを一括、定時、間隔で自動送信し、迅速かつ効率的なマーケティング自動化を実現します。',
      },
      {
        name: 'リード管理',
        description:
          '数百万の営業リードを自動で収集、追跡、管理し、潜在顧客を体系的にフォローアップして、販売転換率を向上させます。',
      },
      {
        name: 'AIによるレポート',
        description:
          '定期的にAI戦略と自動化プロセスを提案し、あなたの決定後に実行します。AIは定期的にレポートも生成します。',
      },
      {
        name: 'ワンストップソリューション',
        description:
          '複雑な専門ソフトウェアは不要です。Bika.aiの軽量AI自動化データベースは、顧客データの保存、管理、追跡のニーズを満たします。',
      },
      {
        name: 'カスタム編集',
        description:
          'Bika.aiは強力なローコード/ノーコードエディターを提供し、さまざまな自動化タスクプロセスとデータシステムを簡単にカスタマイズでき、プロジェクト管理、製品ワークオーダー、注文管理などの多くのアプリケーションシナリオを実現します。',
      },
    ],
    usp: 'Bika.aiは開封即使用の自動化データベースを提供し、豊富な機能とサードパーティ統合が内蔵されています。データ量がどれだけ多くても、数十億のデータであっても、Bika.aiは容易に対応できます。Bika.aiを使用すると、AIと絶え間なく対話する必要はなく、データ量ももはや問題ではありません。\n\nBika.aiを通じてタスクを自動で完了させることで、作業はより効率的かつ正確になり、大量の時間を節約できます。ユーザーは自動化テンプレートを簡単に公開、共有、複製することができ、持続的な改善を容易にします。マーケティング、セールス、プロジェクト管理をよりシンプルにし、AIオートメーションを通じてデータ処理能力を向上させたい場合、Bika.aiはあなたの理想的な選択です。',
    video_ai_agent: 'https://www.youtube.com/embed/POLa4KmVtVo?si=EdkgoshBfbHbCmML',
    video_automation: 'https://www.youtube.com/embed/g0WOF2hkSH0?si=k_hZ-m0BDdcyuSVg',
    video_dashboard: 'https://www.youtube.com/embed/VsrUHkjbbbU?si=1K6XO_liycfxNyyc',
    video_database: 'https://www.youtube.com/embed/BdP9qskz89s?si=KbWMCzSFsu9OQzVG',
    video_documents: 'https://www.youtube.com/embed/XuWV2nSvvoA?si=vYmwS-JUxduCAJMd',
    video_forms: 'https://www.youtube.com/embed/Wi6scmIzDKE?si=ggjeyXaTI2KGjPbw',
    video_introduction: 'https://www.youtube.com/embed/t_f0ZI1VpY4?si=iPrELXmvnZkEPJA6',
    video_onboarding: 'https://www.youtube.com/embed/CxJFssdj6hs?si=uE-30GbTyRCMM4kP',
    video_partners: 'https://www.youtube.com/embed/CxJFssdj6hs?si=uE-30GbTyRCMM4kP',
    video_product: 'https://www.youtube.com/embed/QzmIjaTOjo8?si=tMo3YBd09Na6Yl8f',
  },
  sort: {
    sort_setting: '並べ替え設定',
    sort_title: '並び替え',
  },
  space: {
    advanced: '高度',
    all_members: '私のグループメンバー(サブグループを含む)',
    announcement: 'ワークスペースのホームページのお知らせ',
    default_space_name: '私のスペース',
    email_domain: 'メールドメイン',
    enter_announcement: 'アナウンスメントを入力',
    features_list: 'Space Features List',
    goto_space: 'マイスペースに移動',
    group_members: '私のグループメンバー(サブグループを含まない)',
    home: {
      installed_templates: 'インストールされたテンプレートアプリ',
      invite_members: '友達を招待する',
      set_space_announcement: 'スペースのお知らせを設定する',
      space_announcement: 'スペースのお知らせ',
      view_help_document: 'ヘルプドキュメントを見る',
      view_templates: '利用可能なテンプレートを見る',
    },
    import: 'データのインポート',
    import_description: 'ローカルファイルを直接 Bika 宇宙ステーションにインポートできます。',
    integration: '連携',
    integrations: '連携',
    members: 'メンバー',
    members_and_teams: 'メンバー&チーム',
    msg_go_to_space_settings: '編集と変更',
    msg_space_name_modified_success: '名前の変更が成功しました、新しい名前は: {spaceName}',
    new_space: '新しいスペース',
    no_data: 'データなし',
    no_name: '無名',
    no_permission_content:
      'リンクが正しく、アクセス権があることを確認してください。質問がある場合は、タスク発行者にお問い合わせください。',
    no_permission_title: 'このタスクを表示する権限がありません',
    preview_import: '現在のインポートデータをプレビュー',
    removal_from_space: '宇宙ステーションからの撤去',
    removal_from_space_description:
      'スペースから削除すると、スペース内のすべてのデータが削除されます。',
    role: '役割',
    show_watermark: '透かしを表示',
    space: 'スペース',
    space_creator: 'クリエイター',
    space_domain: 'スペースドメイン',
    space_logo: 'スペースロゴ',
    space_name: 'スペース名',
    space_settings: 'スペース設定',
    space_subdomain: 'スペースサブドメイン',
    teams: 'チーム',
    unnamed: '無名スペース',
    watermark: '透かし',
    you_will_be_assigned_a_subdomain: 'サブドメインが割り当てられます:',
  },
  tags: {
    completed: '完了',
    due: '期限切れ',
    invalid: '失効',
    pending: '進行中',
    read: '既読',
    rejected: '却下',
    request_changed: '変更リクエスト',
    review: '審査',
    unread: '未読',
  },
  task: {
    cutoff_time: '締切時間',
    task: 'タスク',
  },
  team: {
    create_team: 'サブチームを作成',
    delete_team: 'チームを削除',
    delete_team_description:
      'グループを削除すると、元に戻すことはできません。本当にこのグループを削除しますか？',
    delete_teams: '{count} 個のチームを削除',
    edit_team: 'チーム名を編集',
    join_team_description:
      'このリンクから参加したチームメンバーは、自動的にグループとロールに割り当てられます。',
    menu_remove_member_from_space: 'スペースからメンバーを削除',
    menu_remove_member_from_team: 'チームからメンバーを削除',
    msg_add_member_success: 'メンバーを追加しました',
    msg_create_team_success: 'チームを作成しました',
    msg_delete_team_error: 'チームの削除に失敗しました',
    msg_delete_team_success: 'チームを削除しました',
    msg_remove_member_from_space_success: 'スペースからメンバーを削除しました',
    msg_remove_member_from_team_success: 'チームからメンバーを削除しました',
    msg_rename_team_success: 'チーム名を変更しました',
    msg_team_name_not_empty: 'チーム名を入力してください',
    placeholder_new_team: 'チーム名を入力してください',
    placeholder_select_members: '現在のグループに追加するメンバーを選択してください',
    remove_index_members: '{{index}}を削除するメンバー',
    remove_member_from_space: 'スペースからメンバーを削除',
    remove_member_from_space_description:
      'メンバーは削除されますが、履歴、コメント、アップロード、ミッション、その他すべてのコンテンツは保持され、削除されません。削除されたメンバーは、検索やフィルターに引き続き表示される可能性があります。',
    remove_member_from_team: 'チームからメンバーを削除',
    remove_member_from_team_description: 'チームからメンバーを削除しますか？',
    select_team: 'メンバーを選択',
    show_ai: 'AIを表示',
    show_all: 'すべて表示',
    show_member: 'メンバーを表示',
    team: 'チーム',
    teams: 'チーム',
    unselect_all: 'すべて選択解除',
  },
  template: {
    ai_create: 'AIコンサルタント',
    architecture: 'アーキテクチャ',
    architecture_description: '{name}のワークフロー',
    change_log: '変更ログ',
    check_original_template: '元のテンプレートを確認',
    coming_soon: '近日公開',
    coming_soon_tooltip: '近日公開，お楽しみに',
    comments: 'コメント',
    delete_template: 'テンプレートを削除',
    delete_template_description: 'テンプレート "{name}" を削除しますか？',
    delete_template_success: 'テンプレート "{name}" を削除しました',
    empty_change_log: '変更ログがありません',
    export: 'エクスポート',
    favorite: 'お気に入り',
    feedback_email: 'あなたのメール',
    feedback_email_placeholder: 'あなたのメールを入力してください',
    feedback_ideas: 'あなたのアイデアは何ですか？',
    feedback_placeholder: 'あなたのアイデアや提案を入力してください',
    feedback_thanks: 'ありがとうございます',
    get: 'インストール',
    install: 'インストール',
    install_template: 'テンプレートをインストール',
    install_toast: 'テンプレートインストール完',
    make_it_faster: 'もっと速く',
    no_readme: 'READMEがありません',
    no_template: 'このカテゴリにはまだテンプレートがありません',
    not_found_template: '見つけたいテンプレートが見つからない場合は、お知らせください',
    official_certification: '公式認証',
    open: '開く',
    read_more: '→ Bika.aiについて詳しく読む',
    readme: 'README',
    release_notes: '変更ログ',
    release_notes_description: '{name}の変更ログ',
    releases_history: 'リリース履歴',
    select_one_space: 'スペースを選択',
    select_space: 'スペースを選択',
    star_success: 'お気に入りに追加しました',
    template: 'テンプレート',
    title: 'テンプレート',
    try_other_templates: '他のテンプレートを試してみる',
    unstar_success: 'お気に入りから削除しました',
    upgrade: 'アップグレード',
    website_description:
      '<%= category %>分野で強力なビジネスAIエージェントと自動化ワークフローを見つけましょう。AIに働いてもらい、時間を節約し、結果を最適化します。',
    website_title:
      '<%= category %> 分野で最高の<%= count %>個のビジネスAIエージェントテンプレートとデータベースワークフロー (<%= year %>年) | Bika.ai',
  },
  theme: {
    colorful_theme: '多色テーマ',
    dark: 'ダーク',
    light: 'ライト',
    single_color_gradient_theme: '単色グラデーションのテーマ',
    system: 'システム',
    theme: 'テーマ',
    theme_blue: '青',
    theme_brown: 'ブラウン',
    theme_color_1: 'テーマカラー1',
    theme_color_2: 'テーマカラー2',
    theme_color_3: 'テーマカラー3',
    theme_color_4: 'テーマカラー4',
    theme_deepPurple: 'ダークパープル',
    theme_green: 'グリーン',
    theme_indigo: 'インジゴ',
    theme_orange: 'オレンジ',
    theme_pink: 'ピンク',
    theme_purple: 'パープル',
    theme_red: '赤',
    theme_tangerine: 'オレンジ',
    theme_teal: 'ブルーグリーン',
    theme_yellow: '黄色い',
  },
  time: {
    hour: '時間',
    minute: '分',
  },
  tips: {
    call_agent_recipient_helper: 'メンバーを指定してAgentと対話させる',
    drop_files_here: 'ファイルをここにドロップ',
    empty: 'データなし',
    invalid_file_type_error:
      '無効なファイル形式: {invalidFileNames}。受け入れ可能な形式: {uploadAccept}',
    no_suitable_resources: '適切なリソースがありません',
    setting_announcement: 'お知らせ設定',
  },
  todo: {
    complete_all: 'すべて完了',
    create: 'ToDoリストの作成',
    create_todo: 'スマートタスクを作成する',
    finished: '完了',
    my: '私の',
    my_created: '私が作成した',
    no_todo_so_far: 'これまでのやることはありません',
    overdue: '期限切れ',
    pending: '保留中',
    recent: '最近',
    rejected: '却下',
    today: '今日',
    todo: 'やること帳',
    todos: 'やること帳',
    unfinished: '未完了',
  },
  toolbar: {
    hide_all: 'すべて隠す',
    hide_fileds: 'フィールドを隠す',
    hide_kanban_grouping: 'グループを隠す',
    previous: '前の',
    show_all: 'すべて表示',
  },
  top_up: {
    choose_top_up_amount: 'チャージ金額を選択',
    no_balance: 'あなたの残高（ざんだか）が足りません',
    read_and_accept_toc: '利用規約を読んで同意します',
    top_up: 'チャージ',
    top_up_success: '入金（にゅうきん）に成功（せいこう）しました',
    your_bika_coins: 'あなたのポイント',
  },
  trash: {
    delete: '完全に削除',
    delete_description: '完全に削除すると元に戻せません',
    delete_title: '項目を完全に削除',
    recover: '復元',
    recover_description: 'この項目は元のパスに復元されます',
    recover_success: '復元に成功しました',
    recover_title: '項目を復元',
    trash: '垃圾桶',
  },
  tutorial: 'チュートリアル',
  unit: {
    pcs: '個',
    row: '行',
    to: {
      admin: '管理者',
      admin_description: 'スペースステーション内のすべての管理者',
      all_members: '全メンバー',
      all_members_description: 'スペースステーション内のすべてのメンバー',
      current_operator: '現在のオペレーター',
      current_operator_description: '現在のオペレーター',
      email_field: 'メールフィールド',
      email_field_description: 'データベースのメールフィールドからメールを選択してください',
      email_string: 'メール',
      email_subject_and_content: 'メールの件名と内容',
      member_field: '表のメンバーフィールド',
      member_field_description: 'データベースのメンバーフィールドでメンバーを選択してください',
      recipient: '受信者',
      recipient_and_more: '受信者とその他の設定',
      recipient_description: '指定したメンバー、ロール、またはグループを選択します',
      role_select_label: '役割を選択',
      show_more_options: 'その他の設定',
      specify_members_description: 'メンバー、チーム、または役割を選択してください',
      specify_units: 'メンバー、チーム、役割',
      specify_units_description: 'メンバー、チーム、または役割を選択してください',
      team_select_label: 'チームを選択',
      to_title: '検索',
      unit_member: 'メンバー',
      unit_member_description: '指定された1つ以上のメンバーを選択します',
      unit_role: '指定された役割',
      unit_role_description: '指定された1つ以上のロールを選択します',
      unit_team: '指定されたチーム',
      unit_team_description: '指定された1つ以上のグループを選択します',
      user: 'ユーザー',
      user_description: 'スペースステーション内のユーザー、例えば作成者、更新者',
      user_select_label: 'ユーザーを選択',
    },
  },
  unit_selected_modal: {
    empty_data: 'データがありません',
    guest: 'ゲスト',
    organization: '組織',
    role: '役割',
    selected_team: '選択済み',
  },
  upgrade: {
    upgrade: 'アップグレード',
    upgrade_title: '空間をアップグレード',
    upgrade_to_pro: 'Proにアップグレード',
    upgrade_to_pro_button: 'Proにアップグレード',
    upgrade_to_pro_description: 'Proにアップグレードしてさらに多くの機能をアンロックします',
  },
  user: {
    about: '詳細',
    account: '個人アカウント',
    avatar: 'アバター',
    bind_email: 'メールアドレスをバインド',
    bind_phone: '電話番号をバインド',
    change_password: 'パスワードを変更',
    confirm_password: 'パスワードを確認',
    current_password: '現在のパスワード',
    custom_colors: {
      custom: 'カスタム',
      default: 'デフォルト',
      label: 'カスタムカラー（Beta）',
    },
    debug: 'デバッグ',
    download: 'アプリをダウンロード',
    email: 'メールアドレス',
    email_already_bound: 'メールは既にバインドされています',
    email_bind_success: 'バインディング成功',
    email_send_success: '送信成功',
    enter_email: 'メールアドレスを入力してください',
    enter_phone: '電話番号を入力してください',
    enter_verification_code: '認証コードを入力してください',
    get_verification_code: '認証コード取得',
    invite_your_friends_to_register_and_get_1000_bk_coins:
      'あなたの友人を招待して登録し、1,000 ポイントを獲得しましょう',
    language: '言語',
    loading: '読み込み中...',
    member_name: 'メンバー名',
    name: '名前',
    new_email: '新しいメールアドレス',
    new_password: '新しいパスワード',
    nickname: 'ニックネーム',
    no_email: 'メール',
    no_name: '名前なし',
    password: 'パスワード',
    personal_info: '個人情報',
    personal_settings: '個人設定',
    phone: '電話番号',
    phone_already_bound: '電話番号は既にバインドされています',
    phone_bind_success: 'バインディング成功',
    phone_send_success: '送信成功',
    preference: '設定',
    profile: 'プロフィール',
    sessions_current: '現在',
    settings: '設定',
    sign_out: 'サインアウト',
    theme: '表示モード',
    theme_style: {
      bika: 'ビカ',
      dracula: 'ドラキュラ (紫)',
      label: 'テーマカラー',
      solarized: 'ソラライズド (緑)',
    },
    timezone: 'タイムゾーン',
    update_email: 'メールアドレスを変更',
    updated: '成功',
    verification_code: '確認コード',
    verification_code_send_success: '認証コードが送信されました',
    verify_email: 'メールアドレスを確認',
    website: 'ウェブサイト',
  },
  website: {
    about_bika: 'Bikaについて',
    api_doc: 'APIドキュメント',
    architecture_of: 'アーキテクチャ - <%= name %>',
    blog: 'ブログ',
    blog_description:
      'Bika.aiで独自のAIエージェント企業を構築する方法を発見し、ヒントと洞察を得ましょう。',
    blog_title: 'Bika.aiブログ',
    change_region: '言語の切り替え',
    coming_soon: 'この機能はまもなくリリースされます',
    compares: '製品比較',
    contact: {
      contact_discord: 'Discord コミュニティに参加する',
      contact_email: '連絡する',
      contact_form: '営業に連絡',
      contact_us: 'お問い合わせ',
      discord: 'Discord',
      discourse_community: 'コミュニティ',
      done_button: '連絡済み',
      email: 'メール',
      line: 'LINE',
      more: 'その他の問い合わせ方法',
      sales: '営業に連絡',
      scan_code: 'WeChatでQRコードをスキャンしてお問い合わせください',
      support: 'サポート',
      via_discord: '私たちのDiscordコミュニティに参加してください',
      via_email: '***************にメールで連絡してください',
      via_line: 'QRコードをスキャンして、私たちのLINE公式アカウントを追加してください',
      via_sales: '営業部門にお問い合わせください',
      via_support: 'サポートに連絡して問題やバグをフィードバックする',
      via_wecom: 'QRコードをスキャンして、私たちのWeChat公式アカウントを追加してください',
      wechat: 'WeChatウィーチャット',
    },
    contact_sales: '営業に連絡',
    contact_service: 'カスタマーサービスに連絡',
    create_template_with_ai: 'AI作成',
    discover: '発見する',
    help: 'ヘルプ',
    help_center: 'ヘルプセンター',
    help_video: 'ヘルプビデオ',
    install_selfhosted: 'ダウンロード & インストール',
    other_solutions: '他のソリューション',
    price: '価格',
    pricing: {
      description:
        'Bika.aiは、セルフホスティング、オンプレミス、専用クラウドプランなど、柔軟な料金プランを提供し、お客様の特定のニーズに対応します。最適なプランをお選びください。',
      title: '料金プラン',
    },
    search_or_build_ai_app_placeholer: '検索またはAIにニーズを伝える',
    template: 'テンプレート',
    video: {
      marketing: '1.Bika.aiとは？',
      onboarding: '2. クイックスタート',
      product: '3. 詳細を見る',
    },
    visit_website: '公式ウェブサイトを訪問',
  },
  welcome: {
    explore_templates: 'AI自動化テンプレートを探す',
    get_started: '始めましょう',
    message:
      'お会いできて嬉しいです〜 Bika.aiは、スケジュールのリマインダー、生活の記録、顧客管理、チーム管理などに使用できるAI自動化のリマインダーとタスクアシスタントです。',
    mobile_get_started_description: 'ウェブ版を使うには {{web}} をご覧ください',
    more: 'bika.aiのモバイル版をダウンロードすると、マルチ端末でのデータ同期が可能です',
    title: 'こんにちは、私はBikaです',
  },
  widget_config_invalid: 'コンポーネントの設定が無効です。再選択してください',
  widget_database_has_delete: '関連データテーブルが削除されました',
  widget_no_data: 'データなし',
  widget_settings: {
    add_number: '数値を追加',
    add_summary_description: '統計値の説明を追加',
    add_target_value: '目標値を追加',
    all_records: 'すべてのレコード',
    chart_option_database_had_been_deleted: 'データベースが無効です。再選択してください',
    chart_option_field_had_been_deleted: 'フィールドが無効です。再選択してください',
    chart_option_view_had_been_deleted: '無効なビューです。再選択してください',
    column_dimension_field: '列ディメンションとしてフィールドを選択',
    column_dimension_sort_config: '列ディメンションのソート設定',
    exclude_zero_point: 'ゼロ点を除外',
    format_date: '日付をフォーマットする',
    jump_link_url: 'リンクされたデータテーブルに移動',
    more_settings: 'その他の設定',
    null: '[なし]',
    options_config: {
      aggregation_by_field: 'フィールド別集計',
      ai_write: 'AIライティング',
      asc: '昇順',
      avg: '平均',
      bar_chart: '棒グラフ',
      count_records: 'レコード数',
      custom: 'カスタム',
      database: '内蔵データベース',
      default: 'デフォルト',
      desc: '降順',
      filled: '入力済み',
      hidden: '非表示',
      line_chart: '折れ線グラフ',
      max: '最大',
      min: '最小',
      mysql: 'MySQL',
      none: 'なし',
      not_filled: '未入力',
      percent_empty: '空の割合',
      percent_filled: '充填率',
      percent_unique: '一意の割合',
      pie_chart: '円グラフ',
      postgresql: 'PostgreSQL',
      sort_by_x: 'Xでソート',
      sort_by_y: 'Yでソート',
      sum: '合計',
      unique: 'ユニーク',
      year: '年',
      year_month_day_hyphen: '年-月-日',
      year_month_hyphen: '年-月',
      year_season_hyphen: '年-四半期',
      year_week_hyphen: '年-週',
    },
    row_dimension_field: '行ディメンションとしてフィールドを選択',
    row_dimension_sort_config: '行ディメンションのソート設定',
    select_axis_sort: 'ソートする軸を選択',
    select_chart_type: 'チャートタイプを選択',
    select_data_source: 'データソースを選択',
    select_statistics_field: '統計フィールドを選択',
    select_statistics_type: '統計タイプを選択',
    select_theme_color: 'テーマカラーを選択',
    select_view_source: 'データソースとしてビューを選択',
    select_widget_metrics_types: '集計するフィールドを選択',
    select_widget_type: 'ウィジェットタイプを選択',
    separeted_multi_value: '複数の値を区切る',
    set_sort_rules: 'ソートルールを設定',
    show_data_tips: 'データチップを表示',
    show_empty_values: '空の値を表示',
    show_totals: '総計を表示',
    summary_by_field: '集計値としてフィールドを選択',
    widget_Value_x: '値 (X軸)',
    widget_Value_y: '値 (Y軸)',
    widget_dimension: '次元 (X軸)',
    widget_name: 'ウィジェット名',
    widget_operate_delete: 'ウィジェットの削除',
    widget_url_config: 'URL設定',
  },
  widget_title_agenda: '予定',
  widget_title_report: '最近のレポート',
  widget_title_todo: '最近のToDo',
  wizard: {
    agent_engineer: 'エージェントエンジニア',
    ai_wizard: 'AIウィザード',
    ai_wizard_description:
      'AIウィザードは、ユーザーが製品の機能を完全に理解し習得するのを支援するためのインテリジェントなガイドシステムです。使いやすいインターフェースと段階的な紹介を通じて、AIウィザードは各モジュールの使用方法と適用シナリオに慣れるように導きます。',
    already_freshed: '更新済み',
    an_error_occured: 'エラーが発生しました',
    are_sure_to_top_up: 'チャージ確認',
    bika_tip1_description:
      'すべてのワークスペースで、毎日 {count} の無料クレジットを受け取れます。AI とスムーズに協働しましょう。クレジットが足りなくなったら、有料プランを購読してみませんか？今なら期間限定でボーナスクレジットもついてきます！',
    bika_tip1_title: '🚀 メンバーシッププラン開始',
    bika_tips1:
      'Bika.aiはあなたの情熱と創造力によって成長し続けています～今、私たちはメンバーシッププランと機能アップグレードを開始し、すべてのクリエイターにより革新的なAI体験をお届けします！',
    bika_upgraded_tip2_description:
      ' 招待コードの準備ができました！新規ユーザーがあなたの招待コードで登録すると、あなたと友達がそれぞれ{count}を獲得できます。友達を誘って一緒に楽しもう〜',
    bika_upgraded_tip2_title: '👫 友達を招待してポイントを獲得',
    build_scratch_description: '最初から作成して始める',
    build_scratch_name: '🏗️ ゼロから構築',
    check_invite_code: '招待コードを確認',
    choose_tailor_myself:
      'Bika.aiについて十分に理解しており、ゼロから自分専用のAIエージェントとリソースをカスタマイズして構築したいです。',
    close_conversation: '会話を閉じる',
    contact_label:
      'その他の連絡先はありますか？電話番号、WhatsApp、LINE、Facebook、WeChat、電子メールなど。',
    create_space: 'スペースを作成',
    creating: '作成中',
    credits: '{sum}ポイント',
    fill_write_code:
      '有効な招待コードを入力して、システムのすべてのエキサイティングなコンテンツをお楽しみいただき、あなた専用の体験の旅を始めましょう ～',
    filter_by_columns: '{Count}列でフィルタリング済み',
    finance: '📊 ファイナンス',
    finance_description: '資産トラッカー、HR履歴書エージェント向けのエージェントアプリ',
    get_invite_code: 'コミュニティで招待コードを取得',
    industry_job_label: '現在の業界は何ですか？職位は何ですか？',
    installation_completed: 'インストールが完了しました。',
    installer_view_now: '今すぐ表示',
    installer: 'インストーラー',
    invite_code_added: '招待コードの入力が完了し、報酬を獲得しました',
    invite_coin_description: '他の人の招待コードを入力すると、双方が{count}獲得できます',
    marketing: '📣 マーケティング',
    marketing_description:
      'マーケティング向けのエージェントアプリ、AIライター、スライドエージェント、AIデザイナー、メールマーケター、X(Twitter)ソーシャルマネージャーなどを含む',
    membership_modal_title: 'Bika.ai 全面アップグレード！',
    mirror_from: 'ミラー元',
    my_credits: 'マイクレジット',
    navigate_premium_plan: 'プレミアムプランについて',
    new_gift_detail:
      'ご支援ありがとうございます！Bika.aiが追加で{count}をプレゼントします。この気持ちをお受け取りください～',
    new_wizard_created: '新しいウィザードが作成されました',
    official_website: '公式サイト',
    onboarding: 'オンボーディング',
    one_person_company: '👨‍💻 一人会社',
    one_person_company_description:
      '一人会社向けのエージェントアプリ、AIライター、スライドエージェント、AIデザイナー、AIプログラマーなどを含む、',
    placeholder_invite_code: '招待コードを入力してください',
    please_change_invite_code: '招待コードを変更して、もう一度お試しください。',
    product: '📋 プロダクト',
    product_description:
      'プロダクト向けのエージェントアプリ、AIライター、メールマーケター、X(Twitter)ソーシャルマネージャーなどを含む',
    quick_start: 'クイックスタート',
    row_group_by_columns: '既に{Count}列でグループ化されています',
    sales: '💼 営業',
    sales_description:
      '営業向けのエージェントアプリ、AIライター、メールマーケター、X(Twitter)ソーシャルマネージャー、Github Issues作成者などを含む',
    select_role:
      'ロールを選択してください。あらかじめ用意されたAgentic Appsのテンプレートをインストールします。',
    selected_templates: '以下のすべてのテンプレートをインストールします',
    some_columns_hided: '{Count}列を非表示',
    sorted_by_columns: '{Count}列でソート済み',
    support: '🤝 サポート',
    support_description: 'サポート向けのエージェントアプリ、AIライター、メールマーケターなどを含む',
    topup_space_by_credits:
      '現在{count}ポイントをお持ちです。すべて「{spaceName}」にチャージします',
    topup_to_space: 'スペースステーションにチャージ',
    use_case_label:
      'Bika.aiを使用して、どのような使用ケースを解決し、どのような問題を解決したいですか？',
    welcome: 'Bika.aiへようこそ',
    welcome_website: 'Bika.aiへようこそ',
    wizard: 'ウィザード',
    wizard_new_user_gift: '新規ユーザー限定特典！',
    onboarding_role: {
      ceo_name: '👨‍💻 一人会社',
      ceo_description: 'チームのように創造し、実行できる効率的な個人',
      marketer_name: '📢 マーケター',
      marketer_description: '製品のプロモーションとマーケティングを担当する専門家',
      sales_name: '💼 営業',
      sales_description: '製品の販売と顧客関係管理を担当する専門家',
      product_name: '📋 プロダクトマネージャー',
      product_description: '製品の計画、設計、開発を担当する専門家',
      support_name: '🤝 サポート',
      support_description: '顧客サービスと技術サポートを担当する専門家',
      finance_name: '💰 財務または人事',
      finance_description: '会社の財務管理または人事管理を担当する専門家',
    },
  },
};
export default dict;
